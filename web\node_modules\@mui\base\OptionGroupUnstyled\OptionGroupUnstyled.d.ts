import { OverridableComponent } from '@mui/types';
import { OptionGroupUnstyledTypeMap } from './OptionGroupUnstyled.types';
/**
 * An unstyled option group to be used within a SelectUnstyled.
 *
 * Demos:
 *
 * - [Unstyled Select](https://mui.com/base/react-select/)
 *
 * API:
 *
 * - [OptionGroupUnstyled API](https://mui.com/base/api/option-group-unstyled/)
 */
declare const OptionGroupUnstyled: OverridableComponent<OptionGroupUnstyledTypeMap<{}, "li">>;
export default OptionGroupUnstyled;
