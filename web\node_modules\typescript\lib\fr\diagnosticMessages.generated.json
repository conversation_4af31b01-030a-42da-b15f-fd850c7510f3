{"ALL_COMPILER_OPTIONS_6917": "TOUTES LES OPTIONS DU COMPILATEUR", "A_0_modifier_cannot_be_used_with_an_import_declaration_1079": "Impossible d'utiliser un modificateur '{0}' avec une déclaration d'importation.", "A_0_parameter_must_be_the_first_parameter_2680": "Un paramètre '{0}' doit être le premier paramètre.", "A_JSDoc_typedef_comment_may_not_contain_multiple_type_tags_8033": "Un commentaire J<PERSON><PERSON> '@typedef' ne peut pas contenir plusieurs balises '@type'.", "A_bigint_literal_cannot_use_exponential_notation_1352": "Un littéral bigint ne peut pas utiliser la notation exponentielle.", "A_bigint_literal_must_be_an_integer_1353": "Un littéral bigint doit être un entier.", "A_binding_pattern_parameter_cannot_be_optional_in_an_implementation_signature_2463": "Un paramètre de modèle de liaison ne peut pas être facultatif dans une signature d'implémentation.", "A_break_statement_can_only_be_used_within_an_enclosing_iteration_or_switch_statement_1105": "Une instruction 'break' peut être utilisée uniquement dans une itération englobante ou une instruction switch.", "A_break_statement_can_only_jump_to_a_label_of_an_enclosing_statement_1116": "Une instruction 'break' peut accéder uniquement à une étiquette d'une instruction englobante.", "A_class_can_only_implement_an_identifier_Slashqualified_name_with_optional_type_arguments_2500": "Une classe peut uniquement implémenter un identificateur/nom qualifié avec des arguments de type facultatifs.", "A_class_can_only_implement_an_object_type_or_intersection_of_object_types_with_statically_known_memb_2422": "Une classe peut implémenter uniquement un type d'objet ou une intersection de types d'objet avec des membres connus de manière statique.", "A_class_declaration_without_the_default_modifier_must_have_a_name_1211": "Une déclaration de classe sans modificateur 'default' doit porter un nom.", "A_class_member_cannot_have_the_0_keyword_1248": "Un membre de classe ne peut pas avoir le mot clé '{0}'.", "A_comma_expression_is_not_allowed_in_a_computed_property_name_1171": "Une expression avec virgule n'est pas autorisée dans un nom de propriété calculée.", "A_computed_property_name_cannot_reference_a_type_parameter_from_its_containing_type_2467": "Un nom de propriété calculée ne peut pas référencer un paramètre de type à partir de son type conteneur.", "A_computed_property_name_in_a_class_property_declaration_must_have_a_simple_literal_type_or_a_unique_1166": "Un nom de propriété calculée dans une déclaration de propriété de classe doit avoir un type littéral simple ou un type 'unique symbol'.", "A_computed_property_name_in_a_method_overload_must_refer_to_an_expression_whose_type_is_a_literal_ty_1168": "Un nom de propriété calculée dans une surcharge de méthode doit faire référence à une expression dont le type est un type littéral ou un type 'unique symbol'.", "A_computed_property_name_in_a_type_literal_must_refer_to_an_expression_whose_type_is_a_literal_type__1170": "Un nom de propriété calculée dans un littéral de type doit faire référence à une expression dont le type est un type littéral ou un type 'unique symbol'.", "A_computed_property_name_in_an_ambient_context_must_refer_to_an_expression_whose_type_is_a_literal_t_1165": "Un nom de propriété calculée dans un contexte ambiant doit faire référence à une expression dont le type est un type littéral ou un type 'unique symbol'.", "A_computed_property_name_in_an_interface_must_refer_to_an_expression_whose_type_is_a_literal_type_or_1169": "Un nom de propriété calculée dans une interface doit faire référence à une expression dont le type est un type littéral ou un type 'unique symbol'.", "A_computed_property_name_must_be_of_type_string_number_symbol_or_any_2464": "Un nom de propriété calculée doit être de type 'string', 'number', 'symbol' ou 'any'.", "A_const_assertions_can_only_be_applied_to_references_to_enum_members_or_string_number_boolean_array__1355": "Une assertion 'const' peut uniquement être appliquée aux références à des membres enum, des littéraux de chaînes, des littéraux de nombres, des littéraux de valeurs booléennes, des littéraux de tableaux ou des littéraux d'objets.", "A_const_enum_member_can_only_be_accessed_using_a_string_literal_2476": "Un membre d'enum const n'est accessible qu'à l'aide d'un littéral de chaîne.", "A_const_initializer_in_an_ambient_context_must_be_a_string_or_numeric_literal_or_literal_enum_refere_1254": "Un initialiseur 'const' dans un contexte ambiant doit être un littéral de chaîne ou un littéral numérique, ou une référence à un enum littéral.", "A_constructor_cannot_contain_a_super_call_when_its_class_extends_null_17005": "Un constructeur ne peut pas contenir d'appel de 'super' quand sa classe étend 'null'.", "A_constructor_cannot_have_a_this_parameter_2681": "Un constructeur ne peut pas avoir un paramètre 'this'.", "A_continue_statement_can_only_be_used_within_an_enclosing_iteration_statement_1104": "Une instruction 'continue' peut uniquement être utilisée dans une instruction d'itération englobante.", "A_continue_statement_can_only_jump_to_a_label_of_an_enclosing_iteration_statement_1115": "Une instruction 'continue' peut accéder uniquement à une étiquette d'une instruction d'itération englobante.", "A_declare_modifier_cannot_be_used_in_an_already_ambient_context_1038": "Impossible d'utiliser un modificateur 'declare' dans un contexte ambiant déjà défini.", "A_decorator_can_only_decorate_a_method_implementation_not_an_overload_1249": "Un élément décoratif peut uniquement décorer une implémentation de méthode, pas une surcharge.", "A_default_clause_cannot_appear_more_than_once_in_a_switch_statement_1113": "Une clause 'default' ne peut pas figurer plusieurs fois dans une instruction 'switch'.", "A_default_export_can_only_be_used_in_an_ECMAScript_style_module_1319": "Une exportation par défaut ne peut être utilisée que dans un module ECMAScript.", "A_default_export_must_be_at_the_top_level_of_a_file_or_module_declaration_1258": "Une exportation par défaut doit se trouver au niveau supérieur d’une déclaration de fichier ou de module.", "A_definite_assignment_assertion_is_not_permitted_in_this_context_1255": "Une assertion d'affectation définie ' !' n'est pas autorisée dans ce contexte.", "A_destructuring_declaration_must_have_an_initializer_1182": "Une déclaration de déstructuration doit avoir un initialiseur.", "A_dynamic_import_call_in_ES5_SlashES3_requires_the_Promise_constructor_Make_sure_you_have_a_declarat_2712": "Un appel d'importation dynamique en ES5/ES3 nécessite le constructeur 'Promise'. Vérifiez que vous avez une déclaration pour le constructeur 'Promise', ou incluez 'ES2015' dans votre option '--lib'.", "A_dynamic_import_call_returns_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_include_ES20_2711": "Un appel d'importation dynamique retourne 'Promise'. Vérifiez que vous avez une déclaration pour 'Promise', ou incluez 'ES2015' dans votre option '--lib'.", "A_file_cannot_have_a_reference_to_itself_1006": "Un fichier ne peut pas contenir une référence à lui-même.", "A_function_returning_never_cannot_have_a_reachable_end_point_2534": "Une fonction qui retourne 'never' ne peut pas avoir de point de terminaison accessible.", "A_function_that_is_called_with_the_new_keyword_cannot_have_a_this_type_that_is_void_2679": "Une fonction appelée avec le mot clé 'new' ne peut pas avoir un type 'this' dont la valeur est 'void'.", "A_function_whose_declared_type_is_neither_void_nor_any_must_return_a_value_2355": "Une fonction dont le type déclaré n'est ni 'void', ni 'any', doit retourner une valeur.", "A_generator_cannot_have_a_void_type_annotation_2505": "Un générateur ne peut pas avoir d'annotation de type 'void'.", "A_get_accessor_cannot_have_parameters_1054": "Un accesseur 'get' ne peut pas avoir de paramètres.", "A_get_accessor_must_be_at_least_as_accessible_as_the_setter_2808": "Un accesseur get doit être au moins aussi accessible que la méthode setter", "A_get_accessor_must_return_a_value_2378": "Un accesseur 'get' doit retourner une valeur.", "A_label_is_not_allowed_here_1344": "Étiquette non autorisée ici.", "A_labeled_tuple_element_is_declared_as_optional_with_a_question_mark_after_the_name_and_before_the_c_5086": "Un élément de tuple étiqueté est déclaré facultatif avec un point d'interrogation après le nom et avant les deux points, plutôt qu'après le type.", "A_labeled_tuple_element_is_declared_as_rest_with_a_before_the_name_rather_than_before_the_type_5087": "Un élément de tuple étiqueté est déclaré en tant que rest avec '...' avant le nom, plutôt qu'avant le type.", "A_mapped_type_may_not_declare_properties_or_methods_7061": "Un type mappé ne peut pas déclarer de propriétés ou de méthodes.", "A_member_initializer_in_a_enum_declaration_cannot_reference_members_declared_after_it_including_memb_2651": "Un initialiseur de membre dans une déclaration d'enum ne peut pas référencer des membres déclarés après lui, notamment des membres définis dans d'autres enums.", "A_mixin_class_must_have_a_constructor_with_a_single_rest_parameter_of_type_any_2545": "Une classe mixin doit avoir un constructeur avec un paramètre rest unique de type 'any[]'.", "A_mixin_class_that_extends_from_a_type_variable_containing_an_abstract_construct_signature_must_also_2797": "Une classe mixin qui s'étend à partir d'une variable de type contenant une signature de construction abstraite doit également être déclarée 'abstract'.", "A_module_cannot_have_multiple_default_exports_2528": "Un module ne peut pas avoir plusieurs exportations par défaut.", "A_namespace_declaration_cannot_be_in_a_different_file_from_a_class_or_function_with_which_it_is_merg_2433": "Une déclaration d'espace de noms ne peut pas se trouver dans un autre fichier que celui d'une classe ou d'une fonction avec laquelle elle est fusionnée.", "A_namespace_declaration_cannot_be_located_prior_to_a_class_or_function_with_which_it_is_merged_2434": "Une déclaration d'espace de noms ne peut pas se trouver avant une classe ou une fonction avec laquelle elle est fusionnée.", "A_namespace_declaration_is_only_allowed_at_the_top_level_of_a_namespace_or_module_1235": "Une déclaration d’espace de noms n’est autorisée qu’au niveau supérieur d’un espace de noms ou d’un module.", "A_non_dry_build_would_build_project_0_6357": "Une build non-dry va générer le projet '{0}'", "A_non_dry_build_would_delete_the_following_files_Colon_0_6356": "Une build non-dry va supprimer les fichiers suivants : {0}", "A_non_dry_build_would_update_output_of_project_0_6375": "Une build non-dry va mettre à jour la sortie du projet '{0}'", "A_non_dry_build_would_update_timestamps_for_output_of_project_0_6374": "Une build non-dry va mettre à jour les horodatages de la sortie du projet '{0}'", "A_parameter_initializer_is_only_allowed_in_a_function_or_constructor_implementation_2371": "Un initialiseur de paramètre est uniquement autorisé dans une implémentation de fonction ou de constructeur.", "A_parameter_property_cannot_be_declared_using_a_rest_parameter_1317": "Impossible de déclarer une propriété de paramètre à l'aide d'un paramètre rest.", "A_parameter_property_is_only_allowed_in_a_constructor_implementation_2369": "Une propriété de paramètre est uniquement autorisée dans une implémentation de constructeur.", "A_parameter_property_may_not_be_declared_using_a_binding_pattern_1187": "Impossible de déclarer une propriété de paramètre à l'aide d'un modèle de liaison.", "A_promise_must_have_a_then_method_1059": "Une promesse doit avoir une méthode 'then'.", "A_property_of_a_class_whose_type_is_a_unique_symbol_type_must_be_both_static_and_readonly_1331": "Une propriété d'une classe dont le type est un type 'unique symbol' doit être à la fois 'static' et 'readonly'.", "A_property_of_an_interface_or_type_literal_whose_type_is_a_unique_symbol_type_must_be_readonly_1330": "Une propriété d'une interface ou d'un littéral de type dont le type est un type 'unique symbol' doit être 'readonly'.", "A_required_element_cannot_follow_an_optional_element_1257": "Un élément required ne peut pas suivre un élément optional.", "A_required_parameter_cannot_follow_an_optional_parameter_1016": "Un paramètre obligatoire ne peut pas suivre un paramètre optionnel.", "A_rest_element_cannot_contain_a_binding_pattern_2501": "Un élément rest ne peut pas contenir de modèle de liaison.", "A_rest_element_cannot_follow_another_rest_element_1265": "Un élément rest ne peut pas suivre un autre élément rest.", "A_rest_element_cannot_have_a_property_name_2566": "Un élément rest ne peut pas avoir de nom de propriété.", "A_rest_element_cannot_have_an_initializer_1186": "Un élément rest ne peut pas avoir d'initialiseur.", "A_rest_element_must_be_last_in_a_destructuring_pattern_2462": "Un élément rest doit être le dernier dans un modèle de déstructuration.", "A_rest_element_type_must_be_an_array_type_2574": "Un type d'élément rest doit être un type tableau.", "A_rest_parameter_cannot_be_optional_1047": "Un paramètre rest ne peut pas être facultatif.", "A_rest_parameter_cannot_have_an_initializer_1048": "Un paramètre rest ne peut pas avoir d'initialiseur.", "A_rest_parameter_must_be_last_in_a_parameter_list_1014": "Un paramètre rest doit être le dernier dans une liste de paramètres.", "A_rest_parameter_must_be_of_an_array_type_2370": "Un paramètre rest doit être de type tableau.", "A_rest_parameter_or_binding_pattern_may_not_have_a_trailing_comma_1013": "Les modèles de liaison ou les paramètres rest ne doivent pas avoir de virgule de fin.", "A_return_statement_can_only_be_used_within_a_function_body_1108": "Une instruction 'return' peut être utilisée uniquement dans un corps de fonction.", "A_return_statement_cannot_be_used_inside_a_class_static_block_18041": "Une instruction « return » ne peut pas être utilisée à l’intérieur d’un bloc statique de classe.", "A_series_of_entries_which_re_map_imports_to_lookup_locations_relative_to_the_baseUrl_6167": "Série d'entrées qui remappent les importations aux emplacements de recherche en fonction de 'baseUrl'.", "A_set_accessor_cannot_have_a_return_type_annotation_1095": "Un accesseur 'set' ne peut pas avoir d'annotation de type de retour.", "A_set_accessor_cannot_have_an_optional_parameter_1051": "Un accesseur 'set' ne peut pas avoir de paramètre optionnel.", "A_set_accessor_cannot_have_rest_parameter_1053": "Un accesseur 'set' ne peut pas avoir de paramètre rest.", "A_set_accessor_must_have_exactly_one_parameter_1049": "Un accesseur 'set' doit avoir un seul paramètre.", "A_set_accessor_parameter_cannot_have_an_initializer_1052": "Un paramètre d'accesseur 'set' ne peut pas avoir d'initialiseur.", "A_spread_argument_must_either_have_a_tuple_type_or_be_passed_to_a_rest_parameter_2556": "Un argument d’engraissement doit soit avoir un type de tuple, soit être passé à un paramètre REST.", "A_super_call_must_be_a_root_level_statement_within_a_constructor_of_a_derived_class_that_contains_in_2401": "Un appel « super » doit être une instruction de niveau racine dans un constructeur d’une classe dérivée qui contient des propriétés initialisées, des propriétés de paramètre ou des identificateurs privés.", "A_super_call_must_be_the_first_statement_in_the_constructor_to_refer_to_super_or_this_when_a_derived_2376": "Un appel 'super' doit être la première instruction du constructeur à faire référence à « super » ou « this » lorsqu’une classe dérivée contient des propriétés initialisées, des propriétés de paramètre ou des identificateurs privés.", "A_this_based_type_guard_is_not_compatible_with_a_parameter_based_type_guard_2518": "Une protection de type basée sur 'this' n'est pas compatible avec une protection de type basée sur des paramètres.", "A_this_type_is_available_only_in_a_non_static_member_of_a_class_or_interface_2526": "Un type 'this' est disponible uniquement dans un membre non statique d'une classe ou d'une interface.", "A_tsconfig_json_file_is_already_defined_at_Colon_0_5054": "Un fichier 'tsconfig.json' est déjà défini à l'emplacement '{0}'.", "A_tuple_member_cannot_be_both_optional_and_rest_5085": "Un membre de tuple ne peut pas être à la fois facultatif et rest.", "A_tuple_type_cannot_be_indexed_with_a_negative_value_2514": "Un type tuple ne peut pas être indexé avec une valeur négative.", "A_type_assertion_expression_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_expression_Con_17007": "Une expression d'assertion de type n'est pas autorisée dans la partie gauche d'une expression d'élévation à une puissance. Mettez l'expression entre parenthèses.", "A_type_literal_property_cannot_have_an_initializer_1247": "Une propriété de littéral de type ne peut pas avoir d'initialiseur.", "A_type_only_import_can_specify_a_default_import_or_named_bindings_but_not_both_1363": "Une importation de type uniquement peut spécifier une importation par défaut ou des liaisons nommées, mais pas les deux.", "A_type_predicate_cannot_reference_a_rest_parameter_1229": "Un prédicat de type ne peut pas référencer un paramètre rest.", "A_type_predicate_cannot_reference_element_0_in_a_binding_pattern_1230": "Un prédicat de type ne peut pas référencer un élément '{0}' dans un modèle de liaison.", "A_type_predicate_is_only_allowed_in_return_type_position_for_functions_and_methods_1228": "Un prédicat de type est autorisé uniquement dans une position de type de retour pour les fonctions et les méthodes.", "A_type_predicate_s_type_must_be_assignable_to_its_parameter_s_type_2677": "Le type d'un prédicat de type doit être assignable au type de son paramètre.", "A_type_referenced_in_a_decorated_signature_must_be_imported_with_import_type_or_a_namespace_import_w_1272": "Un type référencé dans une signature décorée doit être importé avec « import type » ou une importation d’espace de noms quand « isolatedModules » et « emitDecoratorMetadata » sont activés.", "A_variable_whose_type_is_a_unique_symbol_type_must_be_const_1332": "Une variable dont le type est un type 'unique symbol' doit être 'const'.", "A_yield_expression_is_only_allowed_in_a_generator_body_1163": "Une expression 'yield' est autorisée uniquement dans le corps d'un générateur.", "Abstract_method_0_in_class_1_cannot_be_accessed_via_super_expression_2513": "La méthode abstraite '{0}' de la classe '{1}' n'est pas accessible au moyen de l'expression super.", "Abstract_methods_can_only_appear_within_an_abstract_class_1244": "Les méthodes abstraites peuvent uniquement apparaître dans une classe abstraite.", "Abstract_property_0_in_class_1_cannot_be_accessed_in_the_constructor_2715": "La propriété abstraite '{0}' de la classe '{1}' n'est pas accessible dans le constructeur.", "Accessibility_modifier_already_seen_1028": "Modificateur d'accessibilité déjà rencontré.", "Accessors_are_only_available_when_targeting_ECMAScript_5_and_higher_1056": "Les accesseurs sont uniquement disponibles quand EcmaScript 5 ou version supérieure est ciblé.", "Accessors_must_both_be_abstract_or_non_abstract_2676": "Les accesseurs doivent être abstraits ou non abstraits.", "Add_0_to_unresolved_variable_90008": "Ajouter '{0}.' à la variable non résolue", "Add_a_return_statement_95111": "Ajouter une instruction return", "Add_all_missing_async_modifiers_95041": "Ajouter tous les modificateurs 'async' manquants", "Add_all_missing_attributes_95168": "Ajouter tous les attributs manquants", "Add_all_missing_call_parentheses_95068": "Ajouter toutes les parenthèses d'appel manquantes", "Add_all_missing_function_declarations_95157": "Ajouter toutes les déclarations de fonction manquantes", "Add_all_missing_imports_95064": "Ajouter toutes les importations manquantes", "Add_all_missing_members_95022": "Ajouter tous les membres manquants", "Add_all_missing_override_modifiers_95162": "Ajouter tous les modificateurs 'override' manquants", "Add_all_missing_properties_95166": "Ajouter toutes les propriétés manquantes", "Add_all_missing_return_statement_95114": "Ajouter toutes les instructions return manquantes", "Add_all_missing_super_calls_95039": "Ajouter tous les appels super manquants", "Add_async_modifier_to_containing_function_90029": "A<PERSON><PERSON> le modificateur async dans la fonction conteneur", "Add_await_95083": "Ajouter 'await'", "Add_await_to_initializer_for_0_95084": "Ajouter 'await' à l'initialiseur pour '{0}'", "Add_await_to_initializers_95089": "Ajouter 'await' aux initialiseurs", "Add_braces_to_arrow_function_95059": "Ajouter des accolades à la fonction arrow", "Add_const_to_all_unresolved_variables_95082": "Ajouter 'const' à toutes les variables non résolues", "Add_const_to_unresolved_variable_95081": "Ajouter 'const' à la variable non résolue", "Add_definite_assignment_assertion_to_property_0_95020": "Ajouter une assertion d'assignation définie à la propriété '{0}'", "Add_definite_assignment_assertions_to_all_uninitialized_properties_95028": "Ajouter des assertions d'affectation définie à toutes les propriétés non initialisées", "Add_export_to_make_this_file_into_a_module_95097": "Ajouter 'export {}' pour faire de ce fichier un module", "Add_extends_constraint_2211": "Ajou<PERSON>z la contrainte « extends ».", "Add_extends_constraint_to_all_type_parameters_2212": "Ajouter la contrainte « extends » à tous les paramètres de type", "Add_import_from_0_90057": "Ajouter l'importation de \"{0}\"", "Add_index_signature_for_property_0_90017": "Ajouter une signature d'index pour la propriété '{0}'", "Add_initializer_to_property_0_95019": "Ajouter un initialiseur à la propriété '{0}'", "Add_initializers_to_all_uninitialized_properties_95027": "Ajouter des initialiseurs à toutes les propriétés non initialisées", "Add_missing_attributes_95167": "Ajouter les attributs manquants", "Add_missing_call_parentheses_95067": "Ajouter les parenthèses d'appel manquantes", "Add_missing_enum_member_0_95063": "Ajouter le membre enum manquant '{0}'", "Add_missing_function_declaration_0_95156": "Ajouter la déclaration de fonction manquante '{0}'", "Add_missing_new_operator_to_all_calls_95072": "Ajouter l'opérateur 'new' manquant à tous les appels", "Add_missing_new_operator_to_call_95071": "Ajouter l'opérateur 'new' manquant à l'appel", "Add_missing_properties_95165": "Ajouter des propriétés manquantes", "Add_missing_super_call_90001": "Ajouter l'appel manquant à 'super()'", "Add_missing_typeof_95052": "Ajouter un 'typeof' manquant", "Add_names_to_all_parameters_without_names_95073": "Ajouter des noms à tous les paramètres sans noms", "Add_or_remove_braces_in_an_arrow_function_95058": "Ajouter ou supprimer les accolades dans une fonction arrow", "Add_override_modifier_95160": "Ajouter un modificateur 'override'", "Add_parameter_name_90034": "Ajouter un nom de paramètre", "Add_qualifier_to_all_unresolved_variables_matching_a_member_name_95037": "Ajouter un qualificateur à toutes les variables non résolues correspondant à un nom de membre", "Add_to_all_uncalled_decorators_95044": "Ajouter '()' à tous les décorateurs non appelés", "Add_ts_ignore_to_all_error_messages_95042": "Ajouter '@ts-ignore' à tous les messages d'erreur", "Add_undefined_to_a_type_when_accessed_using_an_index_6674": "<PERSON><PERSON><PERSON><PERSON> « undefined » à un type lorsque vous y accédez à l’aide d’un index.", "Add_undefined_to_optional_property_type_95169": "Ajouter « undefined » à un type de propriété facultatif", "Add_undefined_type_to_all_uninitialized_properties_95029": "Ajouter un type non défini à toutes les propriétés non initialisées", "Add_undefined_type_to_property_0_95018": "Ajouter un type 'undefined' à la propriété '{0}'", "Add_unknown_conversion_for_non_overlapping_types_95069": "Ajouter une conversion 'unknown' pour les types qui ne se chevauchent pas", "Add_unknown_to_all_conversions_of_non_overlapping_types_95070": "Ajouter 'unknown' à toutes les conversions de types qui ne se chevauchent pas", "Add_void_to_Promise_resolved_without_a_value_95143": "Ajouter 'void' à un Promise résolu sans valeur", "Add_void_to_all_Promises_resolved_without_a_value_95144": "Ajouter 'void' à toutes les promesses résolues sans valeur", "Adding_a_tsconfig_json_file_will_help_organize_projects_that_contain_both_TypeScript_and_JavaScript__5068": "L'ajout d'un fichier tsconfig.json permet d'organiser les projets qui contiennent des fichiers TypeScript et JavaScript. En savoir plus sur https://aka.ms/tsconfig.", "All_declarations_of_0_must_have_identical_constraints_2838": "Toutes les déclarations de « {0} » doivent avoir des contraintes identiques.", "All_declarations_of_0_must_have_identical_modifiers_2687": "Toutes les déclarations de '{0}' doivent avoir des modificateurs identiques.", "All_declarations_of_0_must_have_identical_type_parameters_2428": "Toutes les déclarations de '{0}' doivent avoir des paramètres de type identiques.", "All_declarations_of_an_abstract_method_must_be_consecutive_2516": "Toutes les déclarations d'une méthode abstraite doivent être consécutives.", "All_destructured_elements_are_unused_6198": "Tous les éléments déstructurés sont inutilisés.", "All_imports_in_import_declaration_are_unused_6192": "Les importations de la déclaration d'importation ne sont pas toutes utilisées.", "All_type_parameters_are_unused_6205": "Tous les paramètres de type sont inutilisés.", "All_variables_are_unused_6199": "Toutes les variables sont inutilisées.", "Allow_JavaScript_files_to_be_a_part_of_your_program_Use_the_checkJS_option_to_get_errors_from_these__6600": "Autorisez les fichiers JavaScript à faire partie de votre programme. Utilisez l’option « checkJS » pour obtenir des erreurs à partir de ces fichiers.", "Allow_accessing_UMD_globals_from_modules_6602": "Autorisez l'accès aux variables globales UMD à partir des modules.", "Allow_default_imports_from_modules_with_no_default_export_This_does_not_affect_code_emit_just_typech_6011": "Autorisez les importations par défaut à partir des modules sans exportation par défaut. Cela n'affecte pas l'émission du code, juste le contrôle de type.", "Allow_import_x_from_y_when_a_module_doesn_t_have_a_default_export_6601": "Autoriser « importation de x à partir de y » quand un module n’a pas d’exportation par défaut.", "Allow_importing_helper_functions_from_tslib_once_per_project_instead_of_including_them_per_file_6639": "Autorisez l’importation de fonctions d’assistance à partir de tslib une fois par projet, au lieu de les inclure par fichier.", "Allow_javascript_files_to_be_compiled_6102": "Autorisez la compilation des fichiers JavaScript.", "Allow_multiple_folders_to_be_treated_as_one_when_resolving_modules_6691": "Autorisez plusieurs dossiers à être considérés comme un seul lors de la résolution des modules.", "Already_included_file_name_0_differs_from_file_name_1_only_in_casing_1261": "Le nom de fichier déjà inclus '{0}' diffère du nom de fichier '{1}' uniquement par la casse.", "Ambient_module_declaration_cannot_specify_relative_module_name_2436": "Une déclaration de module ambiant ne peut pas spécifier un nom de module relatif.", "Ambient_modules_cannot_be_nested_in_other_modules_or_namespaces_2435": "Impossible d'imbriquer des modules ambiants dans d'autres modules ou espaces de noms.", "An_AMD_module_cannot_have_multiple_name_assignments_2458": "Un module AMD ne peut pas avoir plusieurs affectations de nom.", "An_abstract_accessor_cannot_have_an_implementation_1318": "Un accesseur abstrait ne peut pas avoir d'implémentation.", "An_accessibility_modifier_cannot_be_used_with_a_private_identifier_18010": "Un modificateur d'accessibilité ne peut pas être utilisé avec un identificateur privé.", "An_accessor_cannot_have_type_parameters_1094": "Un accesseur ne peut pas avoir de paramètres de type.", "An_accessor_property_cannot_be_declared_optional_1276": "Une propriété 'accessor' ne peut pas être déclarée comme facultative.", "An_ambient_module_declaration_is_only_allowed_at_the_top_level_in_a_file_1234": "Une déclaration de module ambiant est uniquement autorisée au niveau supérieur dans un fichier.", "An_argument_for_0_was_not_provided_6210": "Aucun argument pour '{0}' n'a été fourni.", "An_argument_matching_this_binding_pattern_was_not_provided_6211": "Aucun argument correspondant à ce modèle de liaison n'a été fourni.", "An_arithmetic_operand_must_be_of_type_any_number_bigint_or_an_enum_type_2356": "Un opérande arithmétique doit être de type 'any', 'number', 'bigint' ou un type enum.", "An_arrow_function_cannot_have_a_this_parameter_2730": "Une fonction arrow ne peut pas avoir un paramètre 'this'.", "An_async_function_or_method_in_ES5_SlashES3_requires_the_Promise_constructor_Make_sure_you_have_a_de_2705": "Une fonction ou méthode asynchrone en ES5/ES3 nécessite le constructeur 'Promise'.  Vérifiez que vous avez une déclaration pour le constructeur 'Promise', ou incluez 'ES2015' dans votre option '--lib'.", "An_async_function_or_method_must_return_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_in_2697": "Une fonction ou une méthode async doit retourner 'Promise'. Vérifiez que vous avez une déclaration pour 'Promise', ou incluez 'ES2015' dans votre option '--lib'.", "An_async_iterator_must_have_a_next_method_2519": "Un itérateur asynchrone doit comporter une méthode 'next()'.", "An_element_access_expression_should_take_an_argument_1011": "Une expression d'accès à un élément doit accepter un argument.", "An_enum_member_cannot_be_named_with_a_private_identifier_18024": "Un membre enum ne peut pas être nommé avec un identificateur privé.", "An_enum_member_cannot_have_a_numeric_name_2452": "Un membre enum ne peut pas avoir un nom numérique.", "An_enum_member_name_must_be_followed_by_a_or_1357": "Un nom de membre enum doit être suivi de ',', de '=' ou de '}'.", "An_expanded_version_of_this_information_showing_all_possible_compiler_options_6928": "Version développée de ces informations, affichant toutes les options possibles du compilateur", "An_export_assignment_cannot_be_used_in_a_module_with_other_exported_elements_2309": "Impossible d'utiliser une assignation d'exportation dans un module comportant d'autres éléments exportés.", "An_export_assignment_cannot_be_used_in_a_namespace_1063": "Une affectation d'exportation ne peut pas être utilisée dans un espace de noms.", "An_export_assignment_cannot_have_modifiers_1120": "Une assignation d'exportation ne peut pas avoir de modificateurs.", "An_export_assignment_must_be_at_the_top_level_of_a_file_or_module_declaration_1231": "Une affectation d’exportation doit se trouver au niveau supérieur d’une déclaration de fichier ou de module.", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_module_1474": "Une déclaration d’exportation ne peut être utilisée qu’au niveau supérieur d’un module.", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1233": "Une déclaration d’exportation ne peut être utilisée qu’au niveau supérieur d’un espace de noms ou d’un module.", "An_export_declaration_cannot_have_modifiers_1193": "Une déclaration d'exportation ne peut pas avoir de modificateurs.", "An_expression_of_type_void_cannot_be_tested_for_truthiness_1345": "Impossible de tester une expression de type 'void' pour déterminer si elle a la valeur true.", "An_extended_Unicode_escape_value_must_be_between_0x0_and_0x10FFFF_inclusive_1198": "Une valeur d'échappement Unicode étendue doit être comprise entre 0x0 et 0x10FFFF inclus.", "An_identifier_or_keyword_cannot_immediately_follow_a_numeric_literal_1351": "Un identificateur ou un mot clé ne peut pas suivre immédiatement un littéral numérique.", "An_implementation_cannot_be_declared_in_ambient_contexts_1183": "Impossible de déclarer une implémentation dans des contextes ambiants.", "An_import_alias_cannot_reference_a_declaration_that_was_exported_using_export_type_1379": "Un alias d'importation ne peut pas référencer une déclaration exportée à l'aide de 'export type'.", "An_import_alias_cannot_reference_a_declaration_that_was_imported_using_import_type_1380": "Un alias d'importation ne peut pas référencer une déclaration importée à l'aide de 'import type'.", "An_import_alias_cannot_use_import_type_1392": "Un alias d'importation ne peut pas utiliser 'import type'", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_module_1473": "Une déclaration d’importation ne peut être utilisée qu’au niveau supérieur d’un module.", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1232": "Une déclaration d’importation ne peut être utilisée qu’au niveau supérieur d’un espace de noms ou d’un module.", "An_import_declaration_cannot_have_modifiers_1191": "Une déclaration d'importation ne peut pas avoir de modificateurs.", "An_import_path_cannot_end_with_a_0_extension_Consider_importing_1_instead_2691": "Un chemin d'importation ne peut pas finir par une extension '{0}'. Importez '{1}' à la place.", "An_index_signature_cannot_have_a_rest_parameter_1017": "Une signature d'index ne peut pas avoir de paramètre rest.", "An_index_signature_cannot_have_a_trailing_comma_1025": "Une signature d'index ne peut pas avoir de virgule de fin.", "An_index_signature_must_have_a_type_annotation_1021": "Une signature d'index doit avoir une annotation de type.", "An_index_signature_must_have_exactly_one_parameter_1096": "Une signature d'index doit avoir un seul paramètre.", "An_index_signature_parameter_cannot_have_a_question_mark_1019": "Un paramètre de signature d'index ne peut pas contenir de point d'interrogation.", "An_index_signature_parameter_cannot_have_an_accessibility_modifier_1018": "Un paramètre de signature d'index ne peut pas avoir de modificateur d'accessibilité.", "An_index_signature_parameter_cannot_have_an_initializer_1020": "Un paramètre de signature d'index ne peut pas avoir d'initialiseur.", "An_index_signature_parameter_must_have_a_type_annotation_1022": "Un paramètre de signature d'index doit avoir une annotation de type.", "An_index_signature_parameter_type_cannot_be_a_literal_type_or_generic_type_Consider_using_a_mapped_o_1337": "Un type de paramètre de signature d’index ne peut pas être un type littéral ni générique. Envisagez plutôt d’utiliser un type d’objet mappé.", "An_index_signature_parameter_type_must_be_string_number_symbol_or_a_template_literal_type_1268": "Un type de paramètre de signature d’index doit être « string », « number », « symbol » ou un type littéral de modèle.", "An_instantiation_expression_cannot_be_followed_by_a_property_access_1477": "Une expression d’instanciation ne peut pas être suivie d’un accès à la propriété.", "An_interface_can_only_extend_an_identifier_Slashqualified_name_with_optional_type_arguments_2499": "Une interface peut uniquement étendre un identificateur/nom qualifié avec des arguments de type facultatifs.", "An_interface_can_only_extend_an_object_type_or_intersection_of_object_types_with_statically_known_me_2312": "Une interface peut étendre uniquement un type d'objet ou une intersection de types d'objet avec des membres connus de manière statique.", "An_interface_cannot_extend_a_primitive_type_like_0_an_interface_can_only_extend_named_types_and_clas_2840": "Une interface ne peut pas étendre un type primitif comme « {0} » ; une interface peut uniquement étendre des types nommés et des classes.", "An_interface_property_cannot_have_an_initializer_1246": "Une propriété d'interface ne peut pas avoir d'initialiseur.", "An_iterator_must_have_a_next_method_2489": "Un itérateur doit comporter une méthode 'next()'.", "An_jsxFrag_pragma_is_required_when_using_an_jsx_pragma_with_JSX_fragments_17017": "Un pragma @jsxFrag est nécessaire quand un pragma @jsx est utilisé avec des fragments JSX.", "An_object_literal_cannot_have_multiple_get_Slashset_accessors_with_the_same_name_1118": "Un littéral d'objet ne peut pas avoir plusieurs accesseurs get/set portant le même nom.", "An_object_literal_cannot_have_multiple_properties_with_the_same_name_1117": "Un littéral d’objet ne peut pas avoir plusieurs propriétés portant le même nom.", "An_object_literal_cannot_have_property_and_accessor_with_the_same_name_1119": "Un littéral d'objet ne peut pas avoir une propriété et un accesseur portant le même nom.", "An_object_member_cannot_be_declared_optional_1162": "Impossible de déclarer un membre d'objet comme étant facultatif.", "An_optional_chain_cannot_contain_private_identifiers_18030": "Une chaîne facultative ne peut pas contenir d'identificateurs privés.", "An_optional_element_cannot_follow_a_rest_element_1266": "Un élément optional ne peut pas suivre un élément rest.", "An_outer_value_of_this_is_shadowed_by_this_container_2738": "Une valeur externe de 'this' est mise en mémoire fantôme par ce conteneur.", "An_overload_signature_cannot_be_declared_as_a_generator_1222": "Une signature de surcharge ne peut pas être déclarée en tant que générateur.", "An_unary_expression_with_the_0_operator_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_ex_17006": "Une expression unaire avec l'opérateur '{0}' n'est pas autorisée dans la partie gauche d'une expression d'élévation à une puissance. Mettez l'expression entre parenthèses.", "Annotate_everything_with_types_from_JSDoc_95043": "Annoter tout avec des types de JSDoc", "Annotate_with_type_from_JSDoc_95009": "Annoter avec le type de JSDoc", "Another_export_default_is_here_2753": "Une autre valeur par défaut d'exportation se trouve ici.", "Are_you_missing_a_semicolon_2734": "Il vous manque un point-virgule ?", "Argument_expression_expected_1135": "Expression d'argument attendue.", "Argument_for_0_option_must_be_Colon_1_6046": "L'argument de l'option '{0}' doit être {1}.", "Argument_of_dynamic_import_cannot_be_spread_element_1325": "L’argument de l’importation dynamique ne peut pas être un élément de propagation.", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_2345": "L'argument de type '{0}' n'est pas attribuable au paramètre de type '{1}'.", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_with_exactOptionalPropertyTypes_Colon_tr_2379": "L'argument de type '{0}' n'est pas assignable au paramètre de type '{1}' avec 'exactOptionalPropertyTypes : true'. Pensez à ajouter 'undefined' aux types des propriétés de la cible.", "Arguments_for_the_rest_parameter_0_were_not_provided_6236": "Les arguments du paramètre de reste '{0}' n'ont pas été fournis.", "Array_element_destructuring_pattern_expected_1181": "Modèle de déstructuration d'élément de tableau attendu.", "Assertions_require_every_name_in_the_call_target_to_be_declared_with_an_explicit_type_annotation_2775": "Quand vous utilisez des assertions, chaque nom de la cible d'appel doit être déclaré à l'aide d'une annotation de type explicite.", "Assertions_require_the_call_target_to_be_an_identifier_or_qualified_name_2776": "Quand vous utilisez des assertions, la cible d'appel doit être un identificateur ou un nom qualifié.", "Asterisk_Slash_expected_1010": "'.' attendu.", "Augmentations_for_the_global_scope_can_only_be_directly_nested_in_external_modules_or_ambient_module_2669": "Les augmentations de la portée globale ne peuvent être directement imbriquées que dans les modules externes ou les déclarations de modules ambiants.", "Augmentations_for_the_global_scope_should_have_declare_modifier_unless_they_appear_in_already_ambien_2670": "Les augmentations de la portée globale doivent comporter un modificateur 'declare', sauf si elles apparaissent déjà dans un contexte ambiant.", "Auto_discovery_for_typings_is_enabled_in_project_0_Running_extra_resolution_pass_for_module_1_using__6140": "La détection automatique des typages est activée dans le projet '{0}'. Exécution de la passe de résolution supplémentaire pour le module '{1}' à l'aide de l'emplacement du cache '{2}'.", "Await_expression_cannot_be_used_inside_a_class_static_block_18037": "Impossible d’utiliser l’expression Await à l’intérieur d’un bloc statique de classe.", "BUILD_OPTIONS_6919": "OPTIONS DE BUILD", "Backwards_Compatibility_6253": "Rétrocompatibilité", "Base_class_expressions_cannot_reference_class_type_parameters_2562": "Les expressions de classe de base ne peuvent pas référencer les paramètres de type de classe.", "Base_constructor_return_type_0_is_not_an_object_type_or_intersection_of_object_types_with_statically_2509": "Le type de retour '{0}' du constructeur de base n'est pas un type d'objet ou une intersection de types d'objet avec des membres connus de manière statique.", "Base_constructors_must_all_have_the_same_return_type_2510": "Les constructeurs de base doivent tous avoir le même type de retour.", "Base_directory_to_resolve_non_absolute_module_names_6083": "Répertoire de base pour la résolution des noms de modules non absolus.", "BigInt_literals_are_not_available_when_targeting_lower_than_ES2020_2737": "Les littéraux BigInt ne sont pas disponibles quand la version ciblée est antérieure à ES2020.", "Binary_digit_expected_1177": "<PERSON><PERSON><PERSON> binaire attendu.", "Binding_element_0_implicitly_has_an_1_type_7031": "L'élément de liaison '{0}' possède implicitement un type '{1}'.", "Block_scoped_variable_0_used_before_its_declaration_2448": "Variable de portée de bloc '{0}' utilisée avant sa déclaration.", "Build_a_composite_project_in_the_working_directory_6925": "Générer un projet composite dans le répertoire de travail.", "Build_all_projects_including_those_that_appear_to_be_up_to_date_6636": "<PERSON><PERSON><PERSON><PERSON> tous les projets, même ceux qui semblent être à jour.", "Build_one_or_more_projects_and_their_dependencies_if_out_of_date_6364": "Générer un ou plusieurs projets et leurs dépendances (s'ils sont obsolètes)", "Build_option_0_requires_a_value_of_type_1_5073": "L'option de build '{0}' nécessite une valeur de type {1}.", "Building_project_0_6358": "Génération du projet '{0}'...", "COMMAND_LINE_FLAGS_6921": "INDICATEURS DE LIGNE DE COMMANDE", "COMMON_COMMANDS_6916": "COMMANDES COURANTES", "COMMON_COMPILER_OPTIONS_6920": "OPTIONS COURANTES DU COMPILATEUR", "Call_decorator_expression_90028": "Appeler l'expression de l'élément décoratif", "Call_signature_return_types_0_and_1_are_incompatible_2202": "Les types de retour de signature d'appel '{0}' et '{1}' sont incompatibles.", "Call_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7020": "La signature d'appel, qui ne dispose pas d'annotation de type de retour, possède implicitement un type de retour 'any'.", "Call_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2204": "Les signatures d'appel sans arguments ont des types de retour incompatibles : '{0}' et '{1}'.", "Call_target_does_not_contain_any_signatures_2346": "La cible de l'appel ne contient aucune signature.", "Can_only_convert_logical_AND_access_chains_95142": "Conversion uniquement de chaînes logiques ET de chaînes d'accès", "Can_only_convert_named_export_95164": "Peut uniquement convertir l’exportation nommée", "Can_only_convert_property_with_modifier_95137": "La propriété peut uniquement être convertie avec un modificateur", "Can_only_convert_string_concatenation_95154": "Peut uniquement convertir une concaténation de chaîne", "Cannot_access_0_1_because_0_is_a_type_but_not_a_namespace_Did_you_mean_to_retrieve_the_type_of_the_p_2713": "Impossible d'accéder à '{0}.{1}', car '{0}' est un type, mais pas un espace de noms. Voulez-vous plutôt récupérer le type de la propriété '{1}' dans '{0}' avec '{0}[\"{1}\"]' ?", "Cannot_access_ambient_const_enums_when_the_isolatedModules_flag_is_provided_2748": "Impossible d'accéder aux enums const ambiants quand l'indicateur '--isolatedModules' est fourni.", "Cannot_assign_a_0_constructor_type_to_a_1_constructor_type_2672": "Impossible d'assigner un type de constructeur '{0}' à un type de constructeur '{1}'.", "Cannot_assign_an_abstract_constructor_type_to_a_non_abstract_constructor_type_2517": "Impossible d'attribuer un type de constructeur abstrait à un type de constructeur non abstrait.", "Cannot_assign_to_0_because_it_is_a_class_2629": "Impossible d'effectuer l'assignation à '{0}', car il s'agit d'une classe.", "Cannot_assign_to_0_because_it_is_a_constant_2588": "Impossible d'effectuer une assignation à '{0}', car il s'agit d'une constante.", "Cannot_assign_to_0_because_it_is_a_function_2630": "Impossible d'effectuer l'assignation à '{0}', car il s'agit d'une fonction.", "Cannot_assign_to_0_because_it_is_a_namespace_2631": "Impossible d'effectuer l'assignation à '{0}', car il s'agit d'un espace de noms.", "Cannot_assign_to_0_because_it_is_a_read_only_property_2540": "Impossible d'effectuer une assignation à '{0}', car il s'agit d'une propriété en lecture seule.", "Cannot_assign_to_0_because_it_is_an_enum_2628": "Impossible d'effectuer l'assignation à '{0}', car il s'agit d'un enum.", "Cannot_assign_to_0_because_it_is_an_import_2632": "Impossible d'effectuer l'assignation à '{0}', car il s'agit d'une importation.", "Cannot_assign_to_0_because_it_is_not_a_variable_2539": "Impossible d'effectuer une assignation à '{0}', car il ne s'agit pas d'une variable.", "Cannot_assign_to_private_method_0_Private_methods_are_not_writable_2803": "Impossible d'effectuer une assignation à la méthode privée '{0}'. Les méthodes privées ne sont pas accessibles en écriture.", "Cannot_augment_module_0_because_it_resolves_to_a_non_module_entity_2671": "Impossible d'augmenter le module '{0}', car il se résout en une entité non-module.", "Cannot_augment_module_0_with_value_exports_because_it_resolves_to_a_non_module_entity_2649": "Impossible d'augmenter le module '{0}' avec des exportations de valeurs, car il se résout en une entité non-module.", "Cannot_compile_modules_using_option_0_unless_the_module_flag_is_amd_or_system_6131": "Impossible de compiler des modules à l'aide de l'option '{0}' tant que l'indicateur '--module' n'a pas la valeur 'amd' ou 'system'.", "Cannot_create_an_instance_of_an_abstract_class_2511": "Impossible de créer une instance d'une classe abstraite.", "Cannot_delegate_iteration_to_value_because_the_next_method_of_its_iterator_expects_type_1_but_the_co_2766": "Impossible de déléguer l'itération à la valeur, car la méthode 'next' de son itérateur attend le type '{1}', mais le générateur conteneur envoie toujours '{0}'.", "Cannot_export_0_Only_local_declarations_can_be_exported_from_a_module_2661": "Impossible d'exporter '{0}'. Seules les déclarations locales peuvent être exportées à partir d'un module.", "Cannot_extend_a_class_0_Class_constructor_is_marked_as_private_2675": "Impossible d'étendre une classe '{0}'. Le constructeur de classe est marqué comme étant privé.", "Cannot_extend_an_interface_0_Did_you_mean_implements_2689": "Impossible d'étendre une interface '{0}'. V<PERSON><PERSON>z-vous dire 'implements' ?", "Cannot_find_a_tsconfig_json_file_at_the_current_directory_Colon_0_5081": "Le fichier tsconfig.json est introuvable dans le répertoire actif : {0}.", "Cannot_find_a_tsconfig_json_file_at_the_specified_directory_Colon_0_5057": "Le fichier tsconfig.json est introuvable dans le répertoire spécifié : '{0}'.", "Cannot_find_global_type_0_2318": "Le type global '{0}' est introuvable.", "Cannot_find_global_value_0_2468": "La valeur globale '{0}' est introuvable.", "Cannot_find_lib_definition_for_0_2726": "Définition de bibliothèque introuvable pour '{0}'.", "Cannot_find_lib_definition_for_0_Did_you_mean_1_2727": "Définition de bibliothèque introuvable pour '{0}'. Est-ce qu'il ne s'agit pas plutôt de '{1}' ?", "Cannot_find_module_0_Consider_using_resolveJsonModule_to_import_module_with_json_extension_2732": "Le module '{0}' est introuvable. Utilisez '--resolveJsonModule' pour importer le module avec l'extension '.json'.", "Cannot_find_module_0_Did_you_mean_to_set_the_moduleResolution_option_to_node_or_to_add_aliases_to_th_2792": "Le module '{0}' est introuvable. Vouliez-vous affecter à l'option 'moduleResolution' la valeur 'node' ou ajouter des alias à l'option 'paths' ?", "Cannot_find_module_0_or_its_corresponding_type_declarations_2307": "Impossible de localiser le module '{0}' ou les déclarations de type correspondantes.", "Cannot_find_name_0_2304": "Le nom '{0}' est introuvable.", "Cannot_find_name_0_Did_you_mean_1_2552": "Le nom '{0}' est introuvable. Est-ce qu'il ne s'agit pas plutôt de '{1}' ?", "Cannot_find_name_0_Did_you_mean_the_instance_member_this_0_2663": "Le nom '{0}' est introuvable. Voulez-vous utiliser le membre d'instance 'this.{0}' ?", "Cannot_find_name_0_Did_you_mean_the_static_member_1_0_2662": "Le nom '{0}' est introuvable. Voulez-vous utiliser le membre statique '{1}.{0}' ?", "Cannot_find_name_0_Did_you_mean_to_write_this_in_an_async_function_2311": "Le nom « {0} » est introuvable. Voulez-vous écrire ceci dans une fonction asynchrone ?", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2583": "Le nom '{0}' est introuvable. Devez-vous changer votre bibliothèque cible ? Essayez de changer l'option de compilateur 'lib' en '{1}' ou une version ultérieure.", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2584": "Le nom '{0}' est introuvable. Devez-vous changer votre bibliothèque cible ? Essayez de remplacer l'option de compilateur 'lib' pour inclure 'dom'.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2582": "Le nom '{0}' est introuvable. Devez-vous installer des définitions de type pour un exécuteur de tests ? Essayez 'npm i --save-dev @types/jest' ou 'npm i --save-dev @types/mocha'.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2593": "Le nom '{0}' est introuvable. Devez-vous installer des définitions de type pour un exécuteur de tests ? Essayez 'npm i --save-dev @types/jest' ou 'npm i --save-dev @types/mocha', puis ajoutez 'jest' ou 'mocha' au champ types de votre fichier tsconfig.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2581": "Le nom '{0}' est introuvable. Devez-vous installer des définitions de type pour jQuery ? Essayez 'npm i --save-dev @types/jquery'.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2592": "Le nom '{0}' est introuvable. Devez-vous installer des définitions de type pour jQuery ? Essayez 'npm i --save-dev @types/jquery', puis ajoutez 'jquery' au champ types de votre fichier tsconfig.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2580": "Le nom '{0}' est introuvable. Devez-vous installer des définitions de type pour node ? Essayez 'npm i --save-dev @types/node'.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2591": "Le nom '{0}' est introuvable. Devez-vous installer des définitions de type pour node ? Essayez 'npm i --save-dev @types/node', puis ajoutez 'node' au champ types de votre fichier tsconfig.", "Cannot_find_namespace_0_2503": "L'espace de noms '{0}' est introuvable.", "Cannot_find_namespace_0_Did_you_mean_1_2833": "Impossible de trouver l'espace de noms '{0}'. <PERSON><PERSON><PERSON><PERSON>-vous dire '{1}'?", "Cannot_find_parameter_0_1225": "Paramètre '{0}' introuvable.", "Cannot_find_the_common_subdirectory_path_for_the_input_files_5009": "Impossible de trouver le chemin d'accès au sous-répertoire commun pour les fichiers d'entrée.", "Cannot_find_type_definition_file_for_0_2688": "Le fichier de définition de type est introuvable pour '{0}'.", "Cannot_import_type_declaration_files_Consider_importing_0_instead_of_1_6137": "Impossible d'importer les fichiers de déclaration de type. Importez '{0}' à la place de '{1}'.", "Cannot_initialize_outer_scoped_variable_0_in_the_same_scope_as_block_scoped_declaration_1_2481": "Impossible d'initialiser la variable de portée externe '{0}' dans la même portée que celle de la déclaration de portée de bloc '{1}'.", "Cannot_invoke_an_object_which_is_possibly_null_2721": "Impossible d'appeler un objet qui a éventuellement une valeur 'null'.", "Cannot_invoke_an_object_which_is_possibly_null_or_undefined_2723": "Impossible d'appeler un objet qui a éventuellement une valeur 'null' ou 'undefined'.", "Cannot_invoke_an_object_which_is_possibly_undefined_2722": "Impossible d'appeler un objet qui a éventuellement une valeur 'undefined'.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_destructuring__2765": "Impossible d'itérer la valeur, car la méthode 'next' de son itérateur attend le type '{1}', mais la déstructuration de tableau envoie toujours '{0}'.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_spread_will_al_2764": "Impossible d'itérer la valeur, car la méthode 'next' de son itérateur attend le type '{1}', mais la diffusion de tableau envoie toujours '{0}'.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_for_of_will_always_s_2763": "Impossible d'itérer la valeur, car la méthode 'next' de son itérateur attend le type '{1}', mais la boucle for-of envoie toujours '{0}'.", "Cannot_prepend_project_0_because_it_does_not_have_outFile_set_6308": "Impossible de préfixer le projet '{0}', car 'outFile' n'est pas défini", "Cannot_read_file_0_5083": "Impossible de lire le fichier '{0}'.", "Cannot_read_file_0_Colon_1_5012": "Impossible de lire le fichier '{0}' : {1}.", "Cannot_redeclare_block_scoped_variable_0_2451": "Impossible de redéclarer la variable de portée de bloc '{0}'.", "Cannot_redeclare_exported_variable_0_2323": "Impossible de redéclarer la variable exportée '{0}'.", "Cannot_redeclare_identifier_0_in_catch_clause_2492": "Impossible de redéclarer l'identificateur '{0}' dans la clause catch.", "Cannot_start_a_function_call_in_a_type_annotation_1441": "Impossible de démarrer un appel de fonction dans une annotation de type.", "Cannot_update_output_of_project_0_because_there_was_error_reading_file_1_6376": "Impossible de mettre à jour la sortie du projet '{0}', car une erreur s'est produite durant la lecture du fichier '{1}'", "Cannot_use_JSX_unless_the_jsx_flag_is_provided_17004": "Impossible d'utiliser JSX, sauf si l'indicateur '--jsx' est fourni.", "Cannot_use_export_import_on_a_type_or_type_only_namespace_when_the_isolatedModules_flag_is_provided_1269": "Impossible d’utiliser « export import » sur un espace de noms de type ou de type uniquement lorsque l’indicateur « --isolatedModules » est fourni.", "Cannot_use_imports_exports_or_module_augmentations_when_module_is_none_1148": "Impossible d'utiliser des importations, des exportations ou des augmentations de module quand '--module' a la valeur 'none'.", "Cannot_use_namespace_0_as_a_type_2709": "Impossible d'utiliser l'espace de noms '{0}' en tant que type.", "Cannot_use_namespace_0_as_a_value_2708": "Impossible d'utiliser l'espace de noms '{0}' en tant que valeur.", "Cannot_use_this_in_a_static_property_initializer_of_a_decorated_class_2816": "Impossible d'utiliser « this » dans un initialiseur de propriété statique d'une classe décorée.", "Cannot_write_file_0_because_it_will_overwrite_tsbuildinfo_file_generated_by_referenced_project_1_6377": "Impossible d'écrire le fichier '{0}', car il va remplacer le fichier '.tsbuildinfo' généré par le projet référencé '{1}'", "Cannot_write_file_0_because_it_would_be_overwritten_by_multiple_input_files_5056": "Impossible d'écrire le fichier '{0}', car il serait remplacé par plusieurs fichiers d'entrée.", "Cannot_write_file_0_because_it_would_overwrite_input_file_5055": "Impossible d'écrire le fichier '{0}', car cela entraînerait le remplacement du fichier d'entrée.", "Catch_clause_variable_cannot_have_an_initializer_1197": "Une variable de clause catch ne peut pas avoir d'initialiseur.", "Catch_clause_variable_type_annotation_must_be_any_or_unknown_if_specified_1196": "L'annotation de type de variable de la clause catch doit être 'any' ou 'unknown' si elle est spécifiée.", "Change_0_to_1_90014": "Changer '{0}' en '{1}'", "Change_all_extended_interfaces_to_implements_95038": "Remplace<PERSON> toutes les interfaces étendues par 'implements'", "Change_all_jsdoc_style_types_to_TypeScript_95030": "Remplacer tous les types jsdoc-style par TypeScript", "Change_all_jsdoc_style_types_to_TypeScript_and_add_undefined_to_nullable_types_95031": "Remplacer tous les types jsdoc-type par TypeScript (et ajouter '| undefined' aux types nullable)", "Change_extends_to_implements_90003": "Changer 'extends' en 'implements'", "Change_spelling_to_0_90022": "Changer l'orthographe en '{0}'", "Check_for_class_properties_that_are_declared_but_not_set_in_the_constructor_6700": "Recherchez les propriétés de classe déclarées mais non définies dans le constructeur.", "Check_that_the_arguments_for_bind_call_and_apply_methods_match_the_original_function_6697": "Vérifiez que les arguments des méthodes « bind », « call » et « apply » correspondent à la fonction d’origine.", "Checking_if_0_is_the_longest_matching_prefix_for_1_2_6104": "Vérification en cours pour déterminer si '{0}' est le préfixe correspondant le plus long pour '{1}' - '{2}'.", "Circular_definition_of_import_alias_0_2303": "Définition circulaire de l'alias d'importation '{0}'.", "Circularity_detected_while_resolving_configuration_Colon_0_18000": "Circularité détectée durant la résolution de la configuration : {0}", "Circularity_originates_in_type_at_this_location_2751": "La circularité est issue du type à cet emplacement.", "Class_0_defines_instance_member_accessor_1_but_extended_class_2_defines_it_as_instance_member_functi_2426": "La classe '{0}' définit l'accesseur de membre d'instance '{1}', mais la classe étendue '{2}' le définit comme fonction de membre d'instance.", "Class_0_defines_instance_member_function_1_but_extended_class_2_defines_it_as_instance_member_access_2423": "La classe '{0}' définit la fonction de membre d'instance '{1}', mais la classe étendue '{2}' la définit comme accesseur de membre d'instance.", "Class_0_defines_instance_member_property_1_but_extended_class_2_defines_it_as_instance_member_functi_2425": "La classe '{0}' définit la propriété de membre d'instance '{1}', mais la classe étendue '{2}' le définit comme fonction de membre d'instance.", "Class_0_incorrectly_extends_base_class_1_2415": "La classe '{0}' étend de manière incorrecte la classe de base '{1}'.", "Class_0_incorrectly_implements_class_1_Did_you_mean_to_extend_1_and_inherit_its_members_as_a_subclas_2720": "La classe '{0}' implémente de manière incorrecte la classe '{1}'. Voulez-vous vraiment étendre '{1}' et hériter de ses membres en tant que sous-classe ?", "Class_0_incorrectly_implements_interface_1_2420": "La classe '{0}' implémente de manière incorrecte l'interface '{1}'.", "Class_0_used_before_its_declaration_2449": "Classe '{0}' utilisée avant sa déclaration.", "Class_constructor_may_not_be_a_generator_1368": "Le constructeur de classe ne peut pas être un générateur.", "Class_constructor_may_not_be_an_accessor_1341": "Le constructeur de la classe ne peut pas être un accesseur.", "Class_declaration_cannot_implement_overload_list_for_0_2813": "La déclaration de classe ne peut pas implémenter la liste de surcharge pour «{0}».", "Class_declarations_cannot_have_more_than_one_augments_or_extends_tag_8025": "Les déclarations de classes ne peuvent pas avoir plusieurs balises '@augments' ou '@extends'.", "Class_decorators_can_t_be_used_with_static_private_identifier_Consider_removing_the_experimental_dec_18036": "Impossible d'utiliser des éléments décoratifs de classe avec un identificateur privé static. Supprimez l'élément décoratif expérimental.", "Class_name_cannot_be_0_2414": "Le nom de la classe ne peut pas être '{0}'.", "Class_name_cannot_be_Object_when_targeting_ES5_with_module_0_2725": "Le nom de la classe ne peut pas être 'Object' quand ES5 est ciblé avec le module {0}.", "Class_static_side_0_incorrectly_extends_base_class_static_side_1_2417": "Le côté statique de la classe '{0}' étend de manière incorrecte le côté statique de la classe de base '{1}'.", "Classes_can_only_extend_a_single_class_1174": "Les classes ne peuvent étendre qu'une seule classe.", "Classes_may_not_have_a_field_named_constructor_18006": "Les classes n'ont peut-être pas de champ nommé 'constructor'.", "Code_contained_in_a_class_is_evaluated_in_JavaScript_s_strict_mode_which_does_not_allow_this_use_of__1210": "Le code contenu dans une classe est évalué en mode strict JavaScript qui n’autorise pas l’utilisation de « {0} ». Pour plus d’informations, consultez https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode.", "Command_line_Options_6171": "Options de ligne de commande", "Compile_the_project_given_the_path_to_its_configuration_file_or_to_a_folder_with_a_tsconfig_json_6020": "Compilez le projet en fonction du chemin de son fichier config ou d'un dossier contenant 'tsconfig.json'.", "Compiler_Diagnostics_6251": "Diagnostics du compilateur", "Compiler_option_0_expects_an_argument_6044": "L'option de compilateur '{0}' attend an argument.", "Compiler_option_0_may_not_be_used_with_build_5094": "L’option '--{0}' du compilateur ne peut pas être utilisée avec '--build'.", "Compiler_option_0_may_only_be_used_with_build_5093": "Option du compilateur '--{0}' ne peut être utilisée qu’avec '--build'.", "Compiler_option_0_of_value_1_is_unstable_Use_nightly_TypeScript_to_silence_this_error_Try_updating_w_4124": "L’option de compilateur « {0} » de la valeur «{1}» est instable. Utilisez TypeScript nocturne pour désactiver cette erreur. Essayez de mettre à jour avec « npm install -D typescript@next ».", "Compiler_option_0_requires_a_value_of_type_1_5024": "L'option de compilateur '{0}' exige une valeur de type {1}.", "Compiler_reserves_name_0_when_emitting_private_identifier_downlevel_18027": "Le compilateur réserve le nom '{0}' quand il émet un identificateur privé pour une version antérieure.", "Compiles_the_TypeScript_project_located_at_the_specified_path_6927": "Compile le projet TypeScript situé au chemin d’accès spécifié.", "Compiles_the_current_project_tsconfig_json_in_the_working_directory_6923": "Compile le projet actif (tsconfig.json sur le répertoire de travail.)", "Compiles_the_current_project_with_additional_settings_6929": "Compile le projet actif, avec des paramètres supplémentaires.", "Completeness_6257": "Exhaustivité", "Composite_projects_may_not_disable_declaration_emit_6304": "Les projets composites ne doivent pas désactiver l'émission de déclaration.", "Composite_projects_may_not_disable_incremental_compilation_6379": "Les projets composites ne doivent pas désactiver la compilation incrémentielle.", "Computed_from_the_list_of_input_files_6911": "Calculé à partir de la liste des fichiers d’entrée", "Computed_property_names_are_not_allowed_in_enums_1164": "Les noms de propriétés calculées ne sont pas autorisés dans les enums.", "Computed_values_are_not_permitted_in_an_enum_with_string_valued_members_2553": "Les valeurs calculées ne sont pas autorisées dans un enum avec des membres ayant une valeur de chaîne.", "Concatenate_and_emit_output_to_single_file_6001": "Concaténer la sortie et l'émettre vers un seul fichier.", "Conflicting_definitions_for_0_found_at_1_and_2_Consider_installing_a_specific_version_of_this_librar_4090": "Définitions en conflit pour '{0}' sur '{1}' et '{2}'. Installez une version spécifique de cette bibliothèque pour résoudre le conflit.", "Conflicts_are_in_this_file_6201": "Il existe des conflits dans ce fichier.", "Consider_adding_a_declare_modifier_to_this_class_6506": "Envisagez d’ajouter un modificateur « declare » à cette classe.", "Construct_signature_return_types_0_and_1_are_incompatible_2203": "Les types de retour de signature de construction '{0}' et '{1}' sont incompatibles.", "Construct_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7013": "La signature de construction, qui ne dispose pas d'annotation de type de retour, possède implicitement un type de retour 'any'.", "Construct_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2205": "Les signatures de construction sans arguments ont des types de retour incompatibles : '{0}' et '{1}'.", "Constructor_implementation_is_missing_2390": "L'implémentation de constructeur est manquante.", "Constructor_of_class_0_is_private_and_only_accessible_within_the_class_declaration_2673": "Le constructeur de la classe '{0}' est privé et uniquement accessible dans la déclaration de classe.", "Constructor_of_class_0_is_protected_and_only_accessible_within_the_class_declaration_2674": "Le constructeur de la classe '{0}' est protégé et uniquement accessible dans la déclaration de classe.", "Constructor_type_notation_must_be_parenthesized_when_used_in_a_union_type_1386": "La notation de type d'un constructeur doit être placée entre parenthèses quand elle est utilisée dans un type union.", "Constructor_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1388": "La notation de type d'un constructeur doit être placée entre parenthèses quand elle est utilisée dans un type intersection.", "Constructors_for_derived_classes_must_contain_a_super_call_2377": "Les constructeurs pour les classes dérivées doivent contenir un appel de 'super'.", "Containing_file_is_not_specified_and_root_directory_cannot_be_determined_skipping_lookup_in_node_mod_6126": "<PERSON>chier conteneur non spécifié et répertoire racine impossible à déterminer. Recherche ignorée dans le dossier 'node_modules'.", "Containing_function_is_not_an_arrow_function_95128": "La fonction conteneur n'est pas une fonction arrow", "Control_what_method_is_used_to_detect_module_format_JS_files_1475": "Contrôlez la méthode utilisée pour détecter les fichiers JS au format module.", "Conversion_of_type_0_to_type_1_may_be_a_mistake_because_neither_type_sufficiently_overlaps_with_the__2352": "La conversion du type '{0}' en type '{1}' est peut-être une erreur, car aucun type ne chevauche suffisamment l'autre. Si cela est intentionnel, convertissez d'abord l'expression en 'unknown'.", "Convert_0_to_1_in_0_95003": "Convertir '{0}' en '{1} dans {0}'", "Convert_0_to_mapped_object_type_95055": "Convertir '{0}' en type d'objet mappé", "Convert_all_const_to_let_95102": "Convertir tous les 'const' en 'let'", "Convert_all_constructor_functions_to_classes_95045": "Convertir toutes les fonctions de constructeur en classes", "Convert_all_imports_not_used_as_a_value_to_type_only_imports_1374": "Convertir toutes les importations non utilisées en tant que valeur en importations de types uniquement", "Convert_all_invalid_characters_to_HTML_entity_code_95101": "Convertir tous les caractères non valides en code d'entité HTML", "Convert_all_re_exported_types_to_type_only_exports_1365": "Convertir tous les types réexportés en exportations de types uniquement", "Convert_all_require_to_import_95048": "Convertir tous les 'require' en 'import'", "Convert_all_to_async_functions_95066": "Tout convertir en fonctions asynchrones", "Convert_all_to_bigint_numeric_literals_95092": "Tout convertir en littéraux numériques bigint", "Convert_all_to_default_imports_95035": "Convertir tout en importations par défaut", "Convert_all_type_literals_to_mapped_type_95021": "Convertir tous les littéraux de type en type mappé", "Convert_arrow_function_or_function_expression_95122": "Convertir une fonction arrow ou une expression de fonction", "Convert_const_to_let_95093": "Convertir 'const' en 'let'", "Convert_default_export_to_named_export_95061": "Convertir l'exportation par défaut en exportation nommée", "Convert_function_declaration_0_to_arrow_function_95106": "Convertir la déclaration de fonction '{0}' en fonction arrow", "Convert_function_expression_0_to_arrow_function_95105": "Convertir l'expression de fonction '{0}' en fonction arrow", "Convert_function_to_an_ES2015_class_95001": "Convertir la fonction en classe ES2015", "Convert_invalid_character_to_its_html_entity_code_95100": "Convertir un caractère non valide en son code d'entité html", "Convert_named_export_to_default_export_95062": "Convertir l'exportation nommée en exportation par défaut", "Convert_named_imports_to_default_import_95170": "Convertir les importations nommées en importation par défaut", "Convert_named_imports_to_namespace_import_95057": "Convertir les importations nommées en importation d'espace de noms", "Convert_namespace_import_to_named_imports_95056": "Convertir l'importation d'espace de noms en importations nommées", "Convert_overload_list_to_single_signature_95118": "Convertir la liste de surcharge en une seule signature", "Convert_parameters_to_destructured_object_95075": "Convertir les paramètres en objet déstructuré", "Convert_require_to_import_95047": "Convertir 'require' en 'import'", "Convert_to_ES_module_95017": "Convertir en module ES", "Convert_to_a_bigint_numeric_literal_95091": "Convertir en littéral numérique bigint", "Convert_to_anonymous_function_95123": "Convertir en fonction anonyme", "Convert_to_arrow_function_95125": "Convertir en fonction arrow", "Convert_to_async_function_95065": "Convertir en fonction asynchrone", "Convert_to_default_import_95013": "Convertir en importation par défaut", "Convert_to_named_function_95124": "Convertir en fonction nommée", "Convert_to_optional_chain_expression_95139": "Convertir en expression de chaîne facultative", "Convert_to_template_string_95096": "Convertir en chaîne de modèle", "Convert_to_type_only_export_1364": "Convertir en exportation de type uniquement", "Convert_to_type_only_import_1373": "Convertir en importation de type uniquement", "Corrupted_locale_file_0_6051": "Fichier de paramètres régionaux endommagé : {0}.", "Could_not_convert_to_anonymous_function_95153": "Impossible de convertir en fonction anonyme", "Could_not_convert_to_arrow_function_95151": "Impossible de convertir en fonction arrow", "Could_not_convert_to_named_function_95152": "Impossible de convertir en fonction nommée", "Could_not_determine_function_return_type_95150": "Impossible de déterminer le type de retour de la fonction", "Could_not_find_a_containing_arrow_function_95127": "Fonction arrow conteneur introuvable", "Could_not_find_a_declaration_file_for_module_0_1_implicitly_has_an_any_type_7016": "Le fichier de déclaration du module '{0}' est introuvable. '{1}' a implicitement un type 'any'.", "Could_not_find_convertible_access_expression_95140": "L'expression d'accès convertible est introuvable", "Could_not_find_export_statement_95129": "Instruction export introuvable", "Could_not_find_import_clause_95131": "Clause import introuvable", "Could_not_find_matching_access_expressions_95141": "L'expression d'accès correspondante est introuvable", "Could_not_find_name_0_Did_you_mean_1_2570": "Le nom «{0}» est introuvable. V<PERSON><PERSON><PERSON>-vous dire «{1}» ?", "Could_not_find_namespace_import_or_named_imports_95132": "Impossible de localiser l'importation d'espace de noms ou les importations nommées", "Could_not_find_property_for_which_to_generate_accessor_95135": "Impossible de localiser la propriété dont l'accesseur doit être généré", "Could_not_resolve_the_path_0_with_the_extensions_Colon_1_6231": "Impossible de résoudre le chemin '{0}' avec les extensions {1}.", "Could_not_write_file_0_Colon_1_5033": "Impossible d'écrire le fichier '{0}' : {1}.", "Create_source_map_files_for_emitted_JavaScript_files_6694": "Créez des fichiers de mappage source pour les fichiers JavaScript émis.", "Create_sourcemaps_for_d_ts_files_6614": "Créez des mappage de source pour les fichiers d.ts.", "Creates_a_tsconfig_json_with_the_recommended_settings_in_the_working_directory_6926": "Crée un tsconfig.json avec les paramètres recommandés dans le répertoire de travail.", "DIRECTORY_6038": "RÉPERTOIRE", "Declaration_augments_declaration_in_another_file_This_cannot_be_serialized_6232": "Cette déclaration augmente la déclaration dans un autre fichier. Cette opération ne peut pas être sérialisée.", "Declaration_emit_for_this_file_requires_using_private_name_0_An_explicit_type_annotation_may_unblock_9005": "L'émission de déclaration pour ce fichier nécessite l'utilisation du nom privé '{0}'. Une annotation de type explicite peut débloquer l'émission de déclaration.", "Declaration_emit_for_this_file_requires_using_private_name_0_from_module_1_An_explicit_type_annotati_9006": "L'émission de déclaration pour ce fichier nécessite l'utilisation du nom privé '{0}' à partir du module '{1}'. Une annotation de type explicite peut débloquer l'émission de déclaration.", "Declaration_expected_1146": "Déclaration attendue.", "Declaration_name_conflicts_with_built_in_global_identifier_0_2397": "Le nom de la déclaration est en conflit avec l'identificateur global intégré '{0}'.", "Declaration_or_statement_expected_1128": "Déclaration ou instruction attendue.", "Declaration_or_statement_expected_This_follows_a_block_of_statements_so_if_you_intended_to_write_a_d_2809": "Déclaration ou instruction attendue. Ce '=' suit un bloc d'instructions, donc si vous avez l'intention d'écrire une affectation de déstructuration, vous devrez peut-être mettre l'ensemble de l'affectation entre parenthèses.", "Declarations_with_definite_assignment_assertions_must_also_have_type_annotations_1264": "Les déclarations avec des assertions d'affectation définies doivent également avoir des annotations de type.", "Declarations_with_initializers_cannot_also_have_definite_assignment_assertions_1263": "Les déclarations avec des initialiseurs ne peuvent pas avoir également des assertions d'affectation définies.", "Declare_a_private_field_named_0_90053": "<PERSON><PERSON><PERSON><PERSON>ez un champ privé nommé '{0}'.", "Declare_method_0_90023": "<PERSON><PERSON><PERSON><PERSON>er la méthode '{0}'", "Declare_private_method_0_90038": "<PERSON><PERSON><PERSON><PERSON>er la méthode privée '{0}'", "Declare_private_property_0_90035": "Dé<PERSON>larer la propriété privée '{0}'", "Declare_property_0_90016": "Dé<PERSON>larer la propriété '{0}'", "Declare_static_method_0_90024": "<PERSON><PERSON><PERSON>larer la méthode statique '{0}'", "Declare_static_property_0_90027": "Déclarer la propriété statique '{0}'", "Decorator_function_return_type_0_is_not_assignable_to_type_1_1270": "Le type de retour de la fonction de décorateur '{0}' n’est pas attribuable au type '{1}'.", "Decorator_function_return_type_is_0_but_is_expected_to_be_void_or_any_1271": "Le type de retour de la fonction de décorateur est '{0}' mais doit être 'void' ou 'any'.", "Decorators_are_not_valid_here_1206": "Les éléments décoratifs ne sont pas valides ici.", "Decorators_cannot_be_applied_to_multiple_get_Slashset_accessors_of_the_same_name_1207": "Impossible d'appliquer des éléments décoratifs à plusieurs accesseurs get/set du même nom.", "Decorators_may_not_be_applied_to_this_parameters_1433": "Il est possible que les éléments décoratifs ne soient pas appliqués aux paramètres 'this'.", "Decorators_must_precede_the_name_and_all_keywords_of_property_declarations_1436": "Les éléments décoratifs doivent précéder le nom et tous les mots clés des déclarations de propriété.", "Default_catch_clause_variables_as_unknown_instead_of_any_6803": "Les variables de clause catch par défaut sont « unknown » au lieu de « any ».", "Default_export_of_the_module_has_or_is_using_private_name_0_4082": "L'exportation par défaut du module a utilisé ou utilise le nom privé '{0}'.", "Default_library_1424": "Bibliothèque par défaut", "Default_library_for_target_0_1425": "Bibliothèque par défaut pour la cible '{0}'", "Definitions_of_the_following_identifiers_conflict_with_those_in_another_file_Colon_0_6200": "Les définitions des identificateurs suivants sont en conflit avec celles d'un autre fichier : {0}", "Delete_all_unused_declarations_95024": "Supprimer toutes les déclarations inutilisées", "Delete_all_unused_imports_95147": "Supprimer toutes les importations inutilisées", "Delete_all_unused_param_tags_95172": "Supprimer toutes les balises '@param' inutilisées", "Delete_the_outputs_of_all_projects_6365": "Supprimer les sorties de tous les projets.", "Delete_unused_param_tag_0_95171": "Supprimer la balise '@param' inutilisée '{0}'", "Deprecated_Use_jsxFactory_instead_Specify_the_object_invoked_for_createElement_when_targeting_react__6084": "[Déconseillé] Utilisez '--jsxFactory' à la place. Permet de spécifier l'objet appelé pour createElement durant le ciblage de 'react' pour l'émission JSX", "Deprecated_Use_outFile_instead_Concatenate_and_emit_output_to_single_file_6170": "[Déconseillé] Utilisez '--outFile' à la place. Permet de con<PERSON> et d'émettre la sortie vers un seul fichier", "Deprecated_Use_skipLibCheck_instead_Skip_type_checking_of_default_library_declaration_files_6160": "[Déconseillé] Utilisez '--skipLibCheck' à la place. Permet d'ignorer le contrôle de type des fichiers de déclaration de la bibliothèque par défaut.", "Deprecated_setting_Use_outFile_instead_6677": "Paramètre déconseillé. Utilisez « outFile » à la place.", "Did_you_forget_to_use_await_2773": "<PERSON><PERSON>-vous oublié d'utiliser 'await' ?", "Did_you_mean_0_1369": "Est-ce que vous avez voulu utiliser '{0}' ?", "Did_you_mean_for_0_to_be_constrained_to_type_new_args_Colon_any_1_2735": "Est-ce que vous avez voulu que '{0}' soit contraint en tant que type 'new (...args: any[]) => {1}' ?", "Did_you_mean_to_call_this_expression_6212": "Est-ce que vous avez voulu appeler cette expression ?", "Did_you_mean_to_mark_this_function_as_async_1356": "Est-ce que vous avez voulu marquer cette fonction comme étant 'async' ?", "Did_you_mean_to_use_a_Colon_An_can_only_follow_a_property_name_when_the_containing_object_literal_is_1312": "Voulez-vous vraiment utiliser le signe ':' ? Le signe '=' peut suivre uniquement un nom de propriété quand le littéral d'objet conteneur fait partie d'un modèle de déstructuration.", "Did_you_mean_to_use_new_with_this_expression_6213": "Est-ce que vous avez voulu utiliser 'new' avec cette expression ?", "Digit_expected_1124": "<PERSON><PERSON><PERSON>", "Directory_0_does_not_exist_skipping_all_lookups_in_it_6148": "Le répertoire '{0}' n'existe pas. Toutes les recherches associées sont ignorées.", "Directory_0_has_no_containing_package_json_scope_Imports_will_not_resolve_6270": "Le répertoire « {0} » ne comporte pas d'étendue package.json comme contenant. Les importations ne seront pas résolues.", "Disable_adding_use_strict_directives_in_emitted_JavaScript_files_6669": "Désactivez l’ajout de directives « use strict » dans les fichiers JavaScript émis.", "Disable_checking_for_this_file_90018": "Désactiver la vérification de ce fichier", "Disable_emitting_comments_6688": "Désactivez les commentaires émettant.", "Disable_emitting_declarations_that_have_internal_in_their_JSDoc_comments_6701": "Désactivez l’émission de déclarations qui ont « @internal » dans leurs commentaires JSDoc.", "Disable_emitting_files_from_a_compilation_6660": "Désactivez l’émission des fichiers à partir d’une compilation.", "Disable_emitting_files_if_any_type_checking_errors_are_reported_6662": "Désactivez l’émission de fichiers si des erreurs de vérification de type sont signalées.", "Disable_erasing_const_enum_declarations_in_generated_code_6682": "Désactivez l’effacement des déclarations « const enum » dans le code généré.", "Disable_error_reporting_for_unreachable_code_6603": "Désactivez le rapport d’erreurs pour le code inaccessible.", "Disable_error_reporting_for_unused_labels_6604": "Désactivez le rapport d’erreurs pour les étiquettes inutilisées.", "Disable_generating_custom_helper_functions_like_extends_in_compiled_output_6661": "Désactiver la création de fonctions d'assistance personnalisées comme «__extends» dans la sortie compilée.", "Disable_including_any_library_files_including_the_default_lib_d_ts_6670": "Désactivez l’inclusion des fichiers de bibliothèque, y compris la valeur par défaut de lib.d.ts.", "Disable_loading_referenced_projects_6235": "Désactivez le chargement des projets référencés.", "Disable_preferring_source_files_instead_of_declaration_files_when_referencing_composite_projects_6620": "Désactiver la préférence des fichiers sources à la place des fichiers de déclaration lors du référencement des projets composites.", "Disable_reporting_of_excess_property_errors_during_the_creation_of_object_literals_6702": "Désactivez le signalement d’erreurs de propriétés excessives lors de la création de littéraux d’objet.", "Disable_resolving_symlinks_to_their_realpath_This_correlates_to_the_same_flag_in_node_6683": "Désactivez la résolution des liens symboliques vers leur chemin d’accès réel. <PERSON>la correspond au même indicateur dans le nœud.", "Disable_size_limitations_on_JavaScript_projects_6162": "Désactivez les limitations de taille sur les projets JavaScript.", "Disable_solution_searching_for_this_project_6224": "Désactivez la recherche de solutions pour ce projet.", "Disable_strict_checking_of_generic_signatures_in_function_types_6673": "Désactivez la vérification stricte des signatures génériques dans les types de fonction.", "Disable_the_type_acquisition_for_JavaScript_projects_6625": "Désactiver l’acquisition de type pour les projets JavaScript", "Disable_truncating_types_in_error_messages_6663": "Désactivez les types tronqués dans les messages d’erreur.", "Disable_use_of_source_files_instead_of_declaration_files_from_referenced_projects_6221": "Désactivez l'utilisation des fichiers sources à la place des fichiers de déclaration dans les projets référencés.", "Disable_wiping_the_console_in_watch_mode_6684": "Désactiver la réinitialisation de la console en mode espion.", "Disables_inference_for_type_acquisition_by_looking_at_filenames_in_a_project_6616": "Désactive l’inférence pour l’acquisition de type en examinant des noms de fichiers dans un projet.", "Disallow_import_s_require_s_or_reference_s_from_expanding_the_number_of_files_TypeScript_should_add__6672": "Interdire à « import », « require » ou « <reference> » d’étendre le nombre de fichiers que TypeScript doit ajouter à un projet.", "Disallow_inconsistently_cased_references_to_the_same_file_6078": "Interdisez les références dont la casse est incohérente dans le même fichier.", "Do_not_add_triple_slash_references_or_imported_modules_to_the_list_of_compiled_files_6159": "N'ajoutez pas de références avec trois barres obliques, ni de modules importés à la liste des fichiers compilés.", "Do_not_emit_comments_to_output_6009": "Ne pas émettre de commentaires dans la sortie.", "Do_not_emit_declarations_for_code_that_has_an_internal_annotation_6056": "N'émettez pas de déclarations pour du code ayant une annotation '@internal'.", "Do_not_emit_outputs_6010": "N'émettez pas de sorties.", "Do_not_emit_outputs_if_any_errors_were_reported_6008": "N'émettez pas de sortie si des erreurs sont signalées.", "Do_not_emit_use_strict_directives_in_module_output_6112": "N'émettez pas de directives 'use strict' dans une sortie de module.", "Do_not_erase_const_enum_declarations_in_generated_code_6007": "N'effacez pas les déclarations d'enum const dans le code généré.", "Do_not_generate_custom_helper_functions_like_extends_in_compiled_output_6157": "Ne générez pas de fonctions d'assistance personnalisées comme '__extends' dans la sortie compilée.", "Do_not_include_the_default_library_file_lib_d_ts_6158": "N'incluez pas le fichier bibliothèque par défaut (lib.d.ts).", "Do_not_report_errors_on_unreachable_code_6077": "Ne signalez pas les erreurs pour le code inaccessible.", "Do_not_report_errors_on_unused_labels_6074": "Ne signalez pas les erreurs pour les étiquettes inutilisées.", "Do_not_resolve_the_real_path_of_symlinks_6013": "Ne pas résoudre le chemin réel des liens symboliques.", "Do_not_truncate_error_messages_6165": "<PERSON>e tronquez pas les messages d'erreur.", "Duplicate_function_implementation_2393": "Implémentation de fonction en double.", "Duplicate_identifier_0_2300": "Identificateur '{0}' en double.", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_2441": "Identificateur '{0}' en double. Le compilateur réserve le nom '{1}' dans l'étendue de plus haut niveau d'un module.", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_containing_async_func_2529": "Identificateur '{0}' en double. Le compilateur réserve le nom '{1}' dans la portée de plus haut niveau d'un module contenant des fonctions async.", "Duplicate_identifier_0_Compiler_reserves_name_1_when_emitting_super_references_in_static_initializer_2818": "Identificateur en double «{0}». Le compilateur réserve le nom «{1}» lors de l’émission de références « super » dans les initialiseurs statiques.", "Duplicate_identifier_0_Compiler_uses_declaration_1_to_support_async_functions_2520": "Identificateur '{0}' en double. Le compilateur utilise la déclaration '{1}' pour prendre en charge les fonctions async.", "Duplicate_identifier_0_Static_and_instance_elements_cannot_share_the_same_private_name_2804": "Identificateur '{0}' dupliqué. Les éléments statiques et les éléments d'instance ne peuvent pas partager le même nom privé.", "Duplicate_identifier_arguments_Compiler_uses_arguments_to_initialize_rest_parameters_2396": "Identificateur dupliqué 'arguments'. Le compilateur utilise 'arguments' pour initialiser les paramètres rest.", "Duplicate_identifier_newTarget_Compiler_uses_variable_declaration_newTarget_to_capture_new_target_me_2543": "Dupliquez l'identificateur '_newTarget'. Le compilateur utilise la déclaration de variable '_newTarget' pour capturer la référence de méta-propriété 'new.target'.", "Duplicate_identifier_this_Compiler_uses_variable_declaration_this_to_capture_this_reference_2399": "Identificateur dupliqué '_this'. Le compilateur utilise la déclaration de variable '_this' pour capturer la référence 'this'.", "Duplicate_index_signature_for_type_0_2374": "Doublon de signature d’index pour le type « {0} ».", "Duplicate_label_0_1114": "Étiquette '{0}' en double.", "Duplicate_property_0_2718": "<PERSON><PERSON><PERSON><PERSON><PERSON> dupliquée '{0}'.", "Dynamic_import_s_specifier_must_be_of_type_string_but_here_has_type_0_7036": "Le spécificateur de l'importation dynamique doit être de type 'string', mais ici il est de type '{0}'.", "Dynamic_imports_are_only_supported_when_the_module_flag_is_set_to_es2020_es2022_esnext_commonjs_amd__1323": "Les importations dynamiques sont prises en charge uniquement lorsque l’indicateur '--module' a la valeur 'es2020', 'es2022', 'esnext', 'commonjs', 'amd', 'system', 'umd', 'node16', ou 'nodenext'.", "Dynamic_imports_can_only_accept_a_module_specifier_and_an_optional_assertion_as_arguments_1450": "Les importations dynamiques peuvent accepter uniquement un spécificateur de module et une assertion facultative en tant qu’arguments.", "Dynamic_imports_only_support_a_second_argument_when_the_module_option_is_set_to_esnext_node16_or_nod_1324": "Les importations dynamiques prennent uniquement en charge un deuxième argument lorsque l’option « --module » est définie sur « esnext », « node16 » ou « nodenext ».", "Each_member_of_the_union_type_0_has_construct_signatures_but_none_of_those_signatures_are_compatible_2762": "Chaque membre du type union '{0}' a des signatures de construction, mais aucune de ces signatures n'est compatible avec les autres.", "Each_member_of_the_union_type_0_has_signatures_but_none_of_those_signatures_are_compatible_with_each_2758": "Chaque membre du type union '{0}' a des signatures, mais aucune de ces signatures n'est compatible avec les autres.", "Editor_Support_6249": "Prise en charge de l’Éditeur", "Element_implicitly_has_an_any_type_because_expression_of_type_0_can_t_be_used_to_index_type_1_7053": "L'élément a implicitement un type 'any', car l'expression de type '{0}' ne peut pas être utilisée pour indexer le type '{1}'.", "Element_implicitly_has_an_any_type_because_index_expression_is_not_of_type_number_7015": "L'élément possède implicitement un type 'any', car l'expression d'index n'est pas de type 'number'.", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_7017": "L'élément a implicitement un type 'any', car le type '{0}' n'a aucune signature d'index.", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_Did_you_mean_to_call_1_7052": "L'élément a implicitement un type 'any', car le type '{0}' n'a aucune signature d'index. Est-ce que vous avez voulu appeler '{1}' ?", "Emit_6246": "<PERSON><PERSON>", "Emit_ECMAScript_standard_compliant_class_fields_6712": "Émettez des champs de classe conformes à la norme ECMAScript.", "Emit_a_UTF_8_Byte_Order_Mark_BOM_in_the_beginning_of_output_files_6622": "Émettez une marque d'ordre d'octet (BOM) UTF-8 au début des fichiers de sortie.", "Emit_a_single_file_with_source_maps_instead_of_having_a_separate_file_6151": "Émettez un seul fichier avec des mappages de sources au lieu d'avoir un fichier distinct.", "Emit_a_v8_CPU_profile_of_the_compiler_run_for_debugging_6638": "Émettez un profil processeur V8 de l’exécution du compilateur pour le débogage.", "Emit_additional_JavaScript_to_ease_support_for_importing_CommonJS_modules_This_enables_allowSyntheti_6626": "Émettez un code JavaScript supplémentaire pour simplifier la prise en charge de l’importation des modules CommonJS. Cela permet à « allowSyntheticDefaultImports » d’être compatible avec le type.", "Emit_class_fields_with_Define_instead_of_Set_6222": "Émettez des champs de classe avec Define à la place de Set.", "Emit_design_type_metadata_for_decorated_declarations_in_source_files_6624": "Émettez des métadonnées de type conception pour les déclarations décorées dans les fichiers sources.", "Emit_more_compliant_but_verbose_and_less_performant_JavaScript_for_iteration_6621": "Émettez des JavaScript plus conformes, mais plus détaillés et moins performants pour l’itération.", "Emit_the_source_alongside_the_sourcemaps_within_a_single_file_requires_inlineSourceMap_or_sourceMap__6152": "Émettez la source aux côtés des mappages de sources dans un fichier unique. Nécessite la définition de '--inlineSourceMap' ou '--sourceMap'.", "Enable_all_strict_type_checking_options_6180": "Activez toutes les options de contrôle de type strict.", "Enable_color_and_formatting_in_TypeScript_s_output_to_make_compiler_errors_easier_to_read_6685": "Activer la couleur et la mise en forme dans la sortie de TypeScript pour faciliter la lecture des erreurs du compilateur.", "Enable_constraints_that_allow_a_TypeScript_project_to_be_used_with_project_references_6611": "Activez les contraintes qui autorisent l’utilisation d’un projet TypeScript avec des références de projet.", "Enable_error_reporting_for_codepaths_that_do_not_explicitly_return_in_a_function_6667": "Activez le rapport d’erreurs pour les chemins de code qui ne sont pas explicitement renvoyés dans une fonction.", "Enable_error_reporting_for_expressions_and_declarations_with_an_implied_any_type_6665": "Activez le rapport d’erreurs pour les expressions et les déclarations avec un type « any » implicite.", "Enable_error_reporting_for_fallthrough_cases_in_switch_statements_6664": "Activez le rapport d’erreurs pour les cas échoués dans les instructions switch.", "Enable_error_reporting_in_type_checked_JavaScript_files_6609": "Activez le rapport d’erreurs dans les fichiers JavaScript vérifiés par type.", "Enable_error_reporting_when_local_variables_aren_t_read_6675": "Activez le rapport d’erreurs lorsque les variables locales ne sont pas lues.", "Enable_error_reporting_when_this_is_given_the_type_any_6668": "Activez le rapport d’erreurs lorsque « this » reçoit le type « any ».", "Enable_experimental_support_for_TC39_stage_2_draft_decorators_6630": "Activez la prise en charge expérimentale des éléments décoratifs de l’étape 2 de TC39.", "Enable_importing_json_files_6689": "Activer l’importation des fichiers .json.", "Enable_project_compilation_6302": "Activer la compilation du projet", "Enable_strict_bind_call_and_apply_methods_on_functions_6214": "Activez des méthodes 'bind', 'call' et 'apply' strictes sur les fonctions.", "Enable_strict_checking_of_function_types_6186": "Activez la vérification stricte des types de fonction.", "Enable_strict_checking_of_property_initialization_in_classes_6187": "Activez la vérification stricte de l'initialisation des propriétés dans les classes.", "Enable_strict_null_checks_6113": "Activez strict null checks.", "Enable_the_experimentalDecorators_option_in_your_configuration_file_95074": "Activer l'option 'experimentalDecorators' dans votre fichier config", "Enable_the_jsx_flag_in_your_configuration_file_95088": "Activer l'indicateur '--jsx' dans votre fichier config", "Enable_tracing_of_the_name_resolution_process_6085": "Activez le traçage du processus de résolution de noms.", "Enable_verbose_logging_6713": "Activer la journalisation détaillée.", "Enables_emit_interoperability_between_CommonJS_and_ES_Modules_via_creation_of_namespace_objects_for__7037": "Active l'interopérabilité entre les modules CommonJS et ES via la création d'objets d'espace de noms pour toutes les importations. Implique 'allowSyntheticDefaultImports'.", "Enables_experimental_support_for_ES7_decorators_6065": "Active la prise en charge expérimentale des éléments décoratifs ES7.", "Enables_experimental_support_for_emitting_type_metadata_for_decorators_6066": "Active la prise en charge expérimentale pour l'émission des métadonnées de type pour les éléments décoratifs.", "Enforces_using_indexed_accessors_for_keys_declared_using_an_indexed_type_6671": "Applique l’utilisation d’accesseurs indexés pour les clés déclarées à l’aide d’un type indexé.", "Ensure_overriding_members_in_derived_classes_are_marked_with_an_override_modifier_6666": "Vérifiez que les membres de substitution dans les classes dérivées sont marqués avec un modificateur de remplacement.", "Ensure_that_casing_is_correct_in_imports_6637": "Assurez-vous que la casse est correcte dans les importations.", "Ensure_that_each_file_can_be_safely_transpiled_without_relying_on_other_imports_6645": "Assurez-vous que chaque fichier peut être recompilé en toute sécurité sans s’appuyer sur d’autres importations.", "Ensure_use_strict_is_always_emitted_6605": "Assurez-vous que « use strict » est toujours émis.", "Entry_point_for_implicit_type_library_0_1420": "Point d'entrée pour la bibliothèque de types implicites '{0}'", "Entry_point_for_implicit_type_library_0_with_packageId_1_1421": "Point d'entrée pour la bibliothèque de types implicites '{0}' ayant le packageId '{1}'", "Entry_point_of_type_library_0_specified_in_compilerOptions_1417": "Point d'entrée de la bibliothèque de types '{0}' spécifiée dans compilerOptions", "Entry_point_of_type_library_0_specified_in_compilerOptions_with_packageId_1_1418": "Point d'entrée de la bibliothèque de types '{0}' spécifiée dans compilerOptions et ayant le packageId '{1}'", "Enum_0_used_before_its_declaration_2450": "Enum '{0}' utilisé avant sa déclaration.", "Enum_declarations_can_only_merge_with_namespace_or_other_enum_declarations_2567": "Les déclarations enum ne peuvent fusionner qu'avec des espaces de noms ou d'autres déclarations enum.", "Enum_declarations_must_all_be_const_or_non_const_2473": "Les déclarations d'enum doivent toutes être const ou non const.", "Enum_member_expected_1132": "Membre enum attendu.", "Enum_member_must_have_initializer_1061": "Un membre enum doit posséder un initialiseur.", "Enum_name_cannot_be_0_2431": "Le nom d'enum ne peut pas être '{0}'.", "Enum_type_0_has_members_with_initializers_that_are_not_literals_2535": "Le type enum '{0}' a des membres dont les initialiseurs ne sont pas des littéraux.", "Errors_Files_6041": "Fichiers d’erreurs", "Examples_Colon_0_6026": "Exemples : {0}", "Excessive_stack_depth_comparing_types_0_and_1_2321": "Profondeur excessive de la pile pour la comparaison des types '{0}' et '{1}'.", "Expected_0_1_type_arguments_provide_these_with_an_extends_tag_8027": "Arguments de type {0}-{1} attendus ; indiquez-les avec la balise '@extends'.", "Expected_0_arguments_but_got_1_2554": "{0} arguments attendus, mais {1} reçus.", "Expected_0_arguments_but_got_1_Did_you_forget_to_include_void_in_your_type_argument_to_Promise_2794": "{0} arguments attendus, mais {1} reçus. <PERSON>z-vous oublié d'inclure 'void' dans votre argument de type pour 'Promise' ?", "Expected_0_type_arguments_but_got_1_2558": "{0} arguments de type attendus, mais {1} reçus.", "Expected_0_type_arguments_provide_these_with_an_extends_tag_8026": "Arguments de type {0} attendus ; indiquez-les avec la balise '@extends'.", "Expected_1_argument_but_got_0_new_Promise_needs_a_JSDoc_hint_to_produce_a_resolve_that_can_be_called_2810": "1 argument attendu, mais 0 obtenu. 'new Promise()' a besoin d’un indicateur JSDoc pour produire un 'resolve' qui peut être appelé sans arguments.", "Expected_at_least_0_arguments_but_got_1_2555": "Au moins {0} arguments attendus, mais {1} reçus.", "Expected_corresponding_JSX_closing_tag_for_0_17002": "Balise de fermeture JSX correspondante attendue pour '{0}'.", "Expected_corresponding_closing_tag_for_JSX_fragment_17015": "Balise de fermeture correspondante attendue pour le fragment JSX.", "Expected_for_property_initializer_1442": "« = » attendu pour l’initialiseur de propriété.", "Expected_type_of_0_field_in_package_json_to_be_1_got_2_6105": "Le type attendu du champ '{0}' dans 'package.json' est censé être '{1}'. Obtention de '{2}'.", "Experimental_support_for_decorators_is_a_feature_that_is_subject_to_change_in_a_future_release_Set_t_1219": "La prise en charge expérimentale des éléments décoratifs est une fonctionnalité susceptible de changer dans une prochaine version. Définissez l'option 'experimentalDecorators' dans votre fichier 'tsconfig' ou 'jsconfig' pour supprimer cet avertissement.", "Explicitly_specified_module_resolution_kind_Colon_0_6087": "Spécification explicite du genre de résolution de module : '{0}'.", "Exponentiation_cannot_be_performed_on_bigint_values_unless_the_target_option_is_set_to_es2016_or_lat_2791": "Impossible d'effectuer l'élévation à une puissance sur des valeurs 'bigint' sauf si l'option 'target' a la valeur 'es2016' ou une valeur qui correspond à une version ultérieure.", "Export_0_from_module_1_90059": "Exporter '{0}' à partir du module '{1}'", "Export_all_referenced_locals_90060": "Exporter tous les variables locales référencées", "Export_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_export_default_or__1203": "Vous ne pouvez pas utiliser l'assignation d'exportation pour cibler des modules ECMAScript. Utilisez 'export default' ou un autre format de module à la place.", "Export_assignment_is_not_supported_when_module_flag_is_system_1218": "L'assignation d'exportation n'est pas prise en charge quand l'indicateur '--module' est 'system'.", "Export_declaration_conflicts_with_exported_declaration_of_0_2484": "La déclaration d'exportation est en conflit avec la déclaration exportée de '{0}'.", "Export_declarations_are_not_permitted_in_a_namespace_1194": "Les déclarations d'exportation ne sont pas autorisées dans un espace de noms.", "Export_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6276": "Le spécificateur d’exportation « {0} » n’existe pas dans l’étendue package.json sur le chemin d’accès « {1} ».", "Exported_type_alias_0_has_or_is_using_private_name_1_4081": "L'alias de type exporté '{0}' possède ou utilise le nom privé '{1}'.", "Exported_type_alias_0_has_or_is_using_private_name_1_from_module_2_4084": "L'alias de type exporté '{0}' a ou utilise le nom privé '{1}' du module {2}.", "Exported_variable_0_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4023": "La variable exportée '{0}' possède ou utilise le nom '{1}' du module externe {2}, mais elle ne peut pas être nommée.", "Exported_variable_0_has_or_is_using_name_1_from_private_module_2_4024": "La variable exportée '{0}' possède ou utilise le nom '{1}' du module privé '{2}'.", "Exported_variable_0_has_or_is_using_private_name_1_4025": "La variable exportée '{0}' possède ou utilise le nom privé '{1}'.", "Exports_and_export_assignments_are_not_permitted_in_module_augmentations_2666": "Les exportations et les assignations d'exportation ne sont pas autorisées dans les augmentations de module.", "Expression_expected_1109": "Expression attendue.", "Expression_or_comma_expected_1137": "Expression ou virgule attendue.", "Expression_produces_a_tuple_type_that_is_too_large_to_represent_2800": "L'expression produit un type de tuple trop grand pour être représenté.", "Expression_produces_a_union_type_that_is_too_complex_to_represent_2590": "L'expression produit un type union trop complexe à représenter.", "Expression_resolves_to_super_that_compiler_uses_to_capture_base_class_reference_2402": "Expression résolue en '_super' et utilisée par le compilateur pour capturer la référence de classe de base.", "Expression_resolves_to_variable_declaration_newTarget_that_compiler_uses_to_capture_new_target_meta__2544": "L'expression génère une déclaration de variable '_newTarget' que le compilateur utilise pour capturer la référence de méta-propriété 'new.target'.", "Expression_resolves_to_variable_declaration_this_that_compiler_uses_to_capture_this_reference_2400": "Expression résolue en déclaration de variable '_this' et utilisée par le compilateur pour capturer la référence 'this'.", "Extract_constant_95006": "Extraire la constante", "Extract_function_95005": "Extraire la fonction", "Extract_to_0_in_1_95004": "Extraire vers {0} dans {1}", "Extract_to_0_in_1_scope_95008": "Extraire vers {0} dans la portée {1}", "Extract_to_0_in_enclosing_scope_95007": "Extraire vers {0} dans la portée englobante", "Extract_to_interface_95090": "Extraire vers l'interface", "Extract_to_type_alias_95078": "Extraire vers l'alias de type", "Extract_to_typedef_95079": "Extraire vers typedef", "Extract_type_95077": "Type d'extraction", "FILE_6035": "FICHIER", "FILE_OR_DIRECTORY_6040": "FICHIER OU RÉPERTOIRE", "Failed_to_parse_file_0_Colon_1_5014": "Échec de l'analyse du fichier '{0}' : {1}.", "Fallthrough_case_in_switch_7029": "Case avec fallthrough dans une instruction switch.", "File_0_does_not_exist_6096": "Le fichier '{0}' n'existe pas.", "File_0_does_not_exist_according_to_earlier_cached_lookups_6240": "Selon des recherches mises en cache antérieures, le fichier '{0}' n’existe pas.", "File_0_exist_use_it_as_a_name_resolution_result_6097": "Le fichier '{0}' existe. Utilisez-le comme résultat pour la résolution de noms.", "File_0_exists_according_to_earlier_cached_lookups_6239": "Selon des recherches mises en cache antérieures, le fichier '{0}' existe.", "File_0_has_an_unsupported_extension_The_only_supported_extensions_are_1_6054": "Le fichier '{0}' a une extension non prise en charge. Les seules extensions prises en charge sont {1}.", "File_0_has_an_unsupported_extension_so_skipping_it_6081": "Le fichier '{0}' a une extension non prise en charge. Il est ignoré.", "File_0_is_a_JavaScript_file_Did_you_mean_to_enable_the_allowJs_option_6504": "Le fichier '{0}' est un fichier JavaScript. Est-ce que vous avez voulu activer l'option 'allowJs' ?", "File_0_is_not_a_module_2306": "Le fichier '{0}' n'est pas un module.", "File_0_is_not_listed_within_the_file_list_of_project_1_Projects_must_list_all_files_or_use_an_includ_6307": "Le fichier '{0}' ne figure pas dans la liste de fichiers du projet '{1}'. Les projets doivent lister tous les fichiers ou utiliser un modèle 'include'.", "File_0_is_not_under_rootDir_1_rootDir_is_expected_to_contain_all_source_files_6059": "Le fichier '{0}' ne se trouve pas sous 'rootDir' '{1}'. 'rootDir' est supposé contenir tous les fichiers sources.", "File_0_not_found_6053": "Fichier '{0}' introuvable.", "File_Management_6245": "Gestion de fichiers", "File_change_detected_Starting_incremental_compilation_6032": "Modification de fichier détectée. Démarrage de la compilation incrémentielle...", "File_is_CommonJS_module_because_0_does_not_have_field_type_1460": "Le fichier est un module CommonJS, car « {0} » n’a pas de champ « type »", "File_is_CommonJS_module_because_0_has_field_type_whose_value_is_not_module_1459": "Le fichier est un module CommonJS, car « {0} » a un champ « type » dont la valeur n’est pas « module »", "File_is_CommonJS_module_because_package_json_was_not_found_1461": "Le fichier est un module CommonJS, car « package.json » est introuvable", "File_is_ECMAScript_module_because_0_has_field_type_with_value_module_1458": "Le fichier est un module ECMAScript, car « {0} » a un champ « type » avec la valeur « module »", "File_is_a_CommonJS_module_it_may_be_converted_to_an_ES_module_80001": "Le fichier est un module CommonJS ; il peut être converti en module ES.", "File_is_default_library_for_target_specified_here_1426": "Le fichier représente la bibliothèque par défaut de la cible spécifiée ici.", "File_is_entry_point_of_type_library_specified_here_1419": "Le fichier représente le point d'entrée de la bibliothèque de types spécifiée ici.", "File_is_included_via_import_here_1399": "Le fichier est inclus via une importation ici.", "File_is_included_via_library_reference_here_1406": "Le fichier est inclus via une référence à la bibliothèque ici.", "File_is_included_via_reference_here_1401": "Le fichier est inclus via une référence ici.", "File_is_included_via_type_library_reference_here_1404": "Le fichier est inclus via une référence à la bibliothèque de types ici.", "File_is_library_specified_here_1423": "Le fichier représente la bibliothèque spécifiée ici.", "File_is_matched_by_files_list_specified_here_1410": "Le fichier correspond à la liste 'files' spécifiée ici.", "File_is_matched_by_include_pattern_specified_here_1408": "Le fichier correspond au modèle include spécifié ici.", "File_is_output_from_referenced_project_specified_here_1413": "Le fichier représente la sortie du projet référencé spécifié ici.", "File_is_output_of_project_reference_source_0_1428": "Le fichier représente la sortie de la source de référence de projet '{0}'", "File_is_source_from_referenced_project_specified_here_1416": "Le fichier représente la source du projet référencé spécifié ici.", "File_name_0_differs_from_already_included_file_name_1_only_in_casing_1149": "Le nom de fichier '{0}' diffère du nom de fichier '{1}' déjà inclus uniquement par la casse.", "File_name_0_has_a_1_extension_stripping_it_6132": "Le nom de fichier '{0}' a une extension '{1}'. Suppression de l'extension.", "File_redirects_to_file_0_1429": "Le fichier est redirigé vers le fichier '{0}'", "File_specification_cannot_contain_a_parent_directory_that_appears_after_a_recursive_directory_wildca_5065": "La spécification de fichier ne peut pas contenir un répertoire parent ('..') après un caractère générique de répertoire récursif ('**') : '{0}'.", "File_specification_cannot_end_in_a_recursive_directory_wildcard_Asterisk_Asterisk_Colon_0_5010": "Une spécification de fichier ne peut pas se terminer par un caractère générique de répertoire récursif ('**') : '{0}'.", "Filters_results_from_the_include_option_6627": "Filtre les résultats de l’option « inclure ».", "Fix_all_detected_spelling_errors_95026": "Corri<PERSON> toutes les fautes d'orthographe détectées", "Fix_all_expressions_possibly_missing_await_95085": "Corriger toutes les expressions où il manque éventuellement 'await'", "Fix_all_implicit_this_errors_95107": "Corriger toutes les erreurs implicites liées à 'this'", "Fix_all_incorrect_return_type_of_an_async_functions_90037": "Corriger tous les types de retour incorrects des fonctions asynchrone", "For_await_loops_cannot_be_used_inside_a_class_static_block_18038": "Les boucles « for await » ne peuvent pas être utilisées à l’intérieur d’un bloc statique de classe.", "Found_0_errors_6217": "{0} erreurs trouvées.", "Found_0_errors_Watching_for_file_changes_6194": "{0} erreurs trouvées. Changements de fichier sous surveillance.", "Found_0_errors_in_1_files_6261": "Erreurs {0} trouvées dans les fichiers {1} .", "Found_0_errors_in_the_same_file_starting_at_Colon_1_6260": "Erreurs {0} trouvées dans le même fichier, à partir de : {1}", "Found_1_error_6216": "1 erreur trouvée.", "Found_1_error_Watching_for_file_changes_6193": "1 erreur trouvée. Changements de fichier sous surveillance.", "Found_1_error_in_1_6259": "1 erreur trouvée dans {1}", "Found_package_json_at_0_6099": "'package.json' trouvé sur '{0}'.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES3_or_ES5_1250": "Les déclarations de fonction ne sont pas autorisées dans les blocs en mode strict durant le ciblage de la version 'ES3' ou 'ES5'.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES3_or_ES5_Class_d_1251": "Les déclarations de fonction ne sont pas autorisées dans les blocs en mode strict durant le ciblage de la version 'ES3' ou 'ES5'. Les définitions de classe sont automatiquement en mode strict.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES3_or_ES5_Modules_1252": "Les déclarations de fonction ne sont pas autorisées dans les blocs en mode strict durant le ciblage de la version 'ES3' ou 'ES5'. Les modules sont automatiquement en mode strict.", "Function_expression_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7011": "L'expression de fonction, qui ne dispose pas d'annotation de type de retour, possède implicitement un type de retour '{0}'.", "Function_implementation_is_missing_or_not_immediately_following_the_declaration_2391": "L'implémentation de fonction est manquante ou ne suit pas immédiatement la déclaration.", "Function_implementation_name_must_be_0_2389": "Le nom de l'implémentation de fonction doit être '{0}'.", "Function_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_ref_7024": "La fonction possède implicitement le type de retour 'any', car elle n'a pas d'annotation de type de retour, et est référencée directement ou indirectement dans l'une de ses expressions de retour.", "Function_lacks_ending_return_statement_and_return_type_does_not_include_undefined_2366": "La fonction n'a pas d'instruction return de fin, et le type de retour n'inclut pas 'undefined'.", "Function_not_implemented_95159": "Fonction non implémentée.", "Function_overload_must_be_static_2387": "La surcharge de fonction doit être statique.", "Function_overload_must_not_be_static_2388": "La surcharge de fonction ne doit pas être statique.", "Function_type_notation_must_be_parenthesized_when_used_in_a_union_type_1385": "La notation de type d'une fonction doit être placée entre parenthèses quand elle est utilisée dans un type union.", "Function_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1387": "La notation de type d'une fonction doit être placée entre parenthèses quand elle est utilisée dans un type intersection.", "Function_type_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7014": "Le type de fonction, qui n'a pas d'annotation de type de retour, a implicitement le type de retour '{0}'.", "Function_with_bodies_can_only_merge_with_classes_that_are_ambient_2814": "La fonction avec des corps ne peut fusionner qu’avec des classes qui sont ambiantes.", "Generate_d_ts_files_from_TypeScript_and_JavaScript_files_in_your_project_6612": "Générez des fichiers .d.ts à partir de fichiers TypeScript et JavaScript dans votre projet.", "Generate_get_and_set_accessors_95046": "<PERSON><PERSON><PERSON>rer les accesseurs 'get' et 'set'", "Generate_get_and_set_accessors_for_all_overriding_properties_95119": "Générer des accesseurs 'get' et 'set' pour toutes les propriétés de remplacement", "Generates_a_CPU_profile_6223": "Génère un profil de processeur.", "Generates_a_sourcemap_for_each_corresponding_d_ts_file_6000": "Génère un mappage de source pour chaque fichier '.d.ts' correspondant.", "Generates_an_event_trace_and_a_list_of_types_6237": "Génère une trace d'événement et une liste de types.", "Generates_corresponding_d_ts_file_6002": "<PERSON><PERSON><PERSON> le <PERSON>er '.d.ts' correspondant.", "Generates_corresponding_map_file_6043": "<PERSON><PERSON><PERSON> le fichier '.map' correspondant.", "Generator_implicitly_has_yield_type_0_because_it_does_not_yield_any_values_Consider_supplying_a_retu_7025": "Le générateur a implicitement le type '{0}', car il ne génère aucune valeur. Indiquez une annotation de type de retour.", "Generators_are_not_allowed_in_an_ambient_context_1221": "Les générateurs ne sont pas autorisés dans un contexte ambiant.", "Generic_type_0_requires_1_type_argument_s_2314": "Le type générique '{0}' exige {1} argument(s) de type.", "Generic_type_0_requires_between_1_and_2_type_arguments_2707": "Le type générique '{0}' nécessite entre {1} et {2} arguments de type.", "Global_module_exports_may_only_appear_at_top_level_1316": "Les exportations de modules globaux ne peuvent apparaître qu'au niveau supérieur.", "Global_module_exports_may_only_appear_in_declaration_files_1315": "Les exportations de modules globaux ne peuvent apparaître que dans les fichiers de déclaration.", "Global_module_exports_may_only_appear_in_module_files_1314": "Les exportations de modules globaux ne peuvent apparaître que dans les fichiers de module.", "Global_type_0_must_be_a_class_or_interface_type_2316": "Le type global '{0}' doit être un type de classe ou d'interface.", "Global_type_0_must_have_1_type_parameter_s_2317": "Le type global '{0}' doit avoir {1} paramètre(s) de type.", "Have_recompiles_in_incremental_and_watch_assume_that_changes_within_a_file_will_only_affect_files_di_6384": "Les recompilations dans '--incremental' et '--watch' supposent que les changements apportés à un fichier affectent uniquement les fichiers qui dépendent directement de ce fichier.", "Have_recompiles_in_projects_that_use_incremental_and_watch_mode_assume_that_changes_within_a_file_wi_6606": "Les recompilations dans les projets qui utilisent le mode « incrémentiel » et « espion » supposent que les modifications au sein d’un fichier affectent uniquement les fichiers directement en fonction de celui-ci.", "Hexadecimal_digit_expected_1125": "Chiffre hexadécimal attendu.", "Identifier_expected_0_is_a_reserved_word_at_the_top_level_of_a_module_1262": "Identificateur attendu. '{0}' est un mot réservé au niveau supérieur d'un module.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_1212": "Identificateur attendu. '{0}' est un mot réservé en mode strict.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Class_definitions_are_automatically_in_stric_1213": "Identificateur attendu. '{0}' est un mot réservé en mode strict. Les définitions de classe sont automatiquement en mode strict.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Modules_are_automatically_in_strict_mode_1214": "Identificateur attendu. '{0}' est un mot réservé en mode strict. Les modules sont automatiquement en mode strict.", "Identifier_expected_0_is_a_reserved_word_that_cannot_be_used_here_1359": "Identificateur attendu. '{0}' est un mot réservé qui ne peut pas être utilisé ici.", "Identifier_expected_1003": "Identificateur attendu.", "Identifier_expected_esModule_is_reserved_as_an_exported_marker_when_transforming_ECMAScript_modules_1216": "Identificateur attendu. '__esModule' est réservé en tant que marqueur exporté durant la transformation des modules ECMAScript.", "Identifier_or_string_literal_expected_1478": "Identificateur ou littéral de chaîne <PERSON>u", "If_the_0_package_actually_exposes_this_module_consider_sending_a_pull_request_to_amend_https_Colon_S_7040": "Si le package '{0}' expose réellement ce module, envoyez une demande de tirage (pull request) pour modifier 'https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/{1}'", "If_the_0_package_actually_exposes_this_module_try_adding_a_new_declaration_d_ts_file_containing_decl_7058": "Si le package' {0} 'expose effectivement ce module, essayez d’ajouter un nouveau fichier de déclaration (. d. TS) contenant’declare module' {1} '; '", "Ignore_this_error_message_90019": "Ignorer ce message d'erreur", "Ignoring_tsconfig_json_compiles_the_specified_files_with_default_compiler_options_6924": "Ignore tsconfig.json, compile les fichiers spécifiés avec les options du compilateur par défaut.", "Implement_all_inherited_abstract_classes_95040": "Implémenter toutes les classes abstraites héritées", "Implement_all_unimplemented_interfaces_95032": "Implémenter toutes les interfaces non implémentées", "Implement_inherited_abstract_class_90007": "Implémenter la classe abstraite héritée", "Implement_interface_0_90006": "Implémenter l'interface '{0}'", "Implements_clause_of_exported_class_0_has_or_is_using_private_name_1_4019": "La clause implements de la classe exportée '{0}' possède ou utilise le nom privé '{1}'.", "Implicit_conversion_of_a_symbol_to_a_string_will_fail_at_runtime_Consider_wrapping_this_expression_i_2731": "La conversion implicite de 'symbol' en 'string' va échouer au moment de l'exécution. Incluez dans un wrapper cette expression en 'String(...)'.", "Import_0_from_1_90013": "Importez '{0}' à partir de \"{1}\".", "Import_assertion_values_must_be_string_literal_expressions_2837": "Les valeurs d’assertion d’importation doivent être des expressions littérales de chaîne.", "Import_assertions_are_not_allowed_on_statements_that_transpile_to_commonjs_require_calls_2836": "Les assertions d’importation ne sont pas autorisées sur les instructions qui transpilent en appels commonjs « require ».", "Import_assertions_are_only_supported_when_the_module_option_is_set_to_esnext_or_nodenext_2821": "Les assertions d’importation ne sont prises en charge que lorsque l’option « --module » a la valeur « esnext » ou « nodenext ».", "Import_assertions_cannot_be_used_with_type_only_imports_or_exports_2822": "Les assertions d’importation ne peuvent pas être utilisées avec les importations ou exportations de type uniquement.", "Import_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_import_Asterisk_as_1202": "Vous ne pouvez pas utiliser l'assignation d'importation pour cibler des modules ECMAScript. Utilisez plutôt 'import * as ns from \"mod\"', 'import {a} from \"mod\"', 'import d from \"mod\"' ou un autre format de module.", "Import_declaration_0_is_using_private_name_1_4000": "La déclaration d'importation '{0}' utilise le nom privé '{1}'.", "Import_declaration_conflicts_with_local_declaration_of_0_2440": "La déclaration d'importation est en conflit avec la déclaration locale de '{0}'.", "Import_declarations_in_a_namespace_cannot_reference_a_module_1147": "Les déclarations d'importation dans un espace de noms ne peuvent pas référencer un module.", "Import_emit_helpers_from_tslib_6139": "Importer l'assistance à l'émission à partir de 'tslib'.", "Import_may_be_converted_to_a_default_import_80003": "L'importation peut être convertie en importation par défaut.", "Import_name_cannot_be_0_2438": "Le nom d'importation ne peut pas être '{0}'.", "Import_or_export_declaration_in_an_ambient_module_declaration_cannot_reference_module_through_relati_2439": "Une déclaration d'importation ou d'exportation dans une déclaration de module ambiant ne peut référencer un module au moyen d'un nom de module relatif.", "Import_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6271": "Le spécificateur d’importation « {0} » n’existe pas dans l’étendue package.json sur le chemin d’accès « {1} ».", "Imported_via_0_from_file_1_1393": "Importé(e) via {0} à partir du fichier '{1}'", "Imported_via_0_from_file_1_to_import_importHelpers_as_specified_in_compilerOptions_1395": "Importé(e) via {0} à partir du fichier '{1}' pour importer 'importHelpers' comme indiqué dans compilerOptions", "Imported_via_0_from_file_1_to_import_jsx_and_jsxs_factory_functions_1397": "Importé(e) via {0} à partir du fichier '{1}' pour importer les fonctions de fabrique 'jsx' et 'jsxs'", "Imported_via_0_from_file_1_with_packageId_2_1394": "Importé(e) via {0} à partir du fichier '{1}' ayant le packageId '{2}'", "Imported_via_0_from_file_1_with_packageId_2_to_import_importHelpers_as_specified_in_compilerOptions_1396": "Importé(e) via {0} à partir du fichier '{1}' ayant le packageId '{2}' pour importer 'importHelpers' comme indiqué dans compilerOptions", "Imported_via_0_from_file_1_with_packageId_2_to_import_jsx_and_jsxs_factory_functions_1398": "Importé(e) via {0} à partir du fichier '{1}' ayant le packageId '{2}' pour importer les fonctions de fabrique 'jsx' et 'jsxs'", "Imports_are_not_permitted_in_module_augmentations_Consider_moving_them_to_the_enclosing_external_mod_2667": "Les importations ne sont pas autorisées dans les augmentations de module. Déplacez-les vers le module externe englobant.", "In_ambient_enum_declarations_member_initializer_must_be_constant_expression_1066": "Dans les déclarations d'enums ambiants, l'initialiseur de membre doit être une expression constante.", "In_an_enum_with_multiple_declarations_only_one_declaration_can_omit_an_initializer_for_its_first_enu_2432": "Dans un enum avec plusieurs déclarations, seule une déclaration peut omettre un initialiseur pour son premier élément d'enum.", "Include_a_list_of_files_This_does_not_support_glob_patterns_as_opposed_to_include_6635": "Incluez une liste de fichiers. Cela ne prend pas en charge les modèles Glob, par opposition à « inclure ».", "Include_modules_imported_with_json_extension_6197": "Inclure les modules importés avec l'extension '.json'", "Include_source_code_in_the_sourcemaps_inside_the_emitted_JavaScript_6644": "Incluez le code source dans les images sources à l’intérieur du Code JavaScript émis.", "Include_sourcemap_files_inside_the_emitted_JavaScript_6643": "Incluez les fichiers sourcemap à l’intérieur du Code JavaScript émis.", "Includes_imports_of_types_referenced_by_0_90054": "Inclut les importations de types référencés par « {0} »", "Including_watch_w_will_start_watching_the_current_project_for_the_file_changes_Once_set_you_can_conf_6914": "En incluant --watch, -w commence à regarder le projet actuel pour les modifications apportées au fichier. Une fois défini, vous pouvez configurer le mode espion avec :", "Index_signature_for_type_0_is_missing_in_type_1_2329": "La signature d’index pour le type « {0} » est manquante dans le type « {1} ».", "Index_signature_in_type_0_only_permits_reading_2542": "La signature d'index du type '{0}' autorise uniquement la lecture.", "Individual_declarations_in_merged_declaration_0_must_be_all_exported_or_all_local_2395": "Les déclarations individuelles de la déclaration fusionnée '{0}' doivent toutes être exportées ou locales.", "Infer_all_types_from_usage_95023": "Déduire tous les types de l'utilisation", "Infer_function_return_type_95148": "Déduire le type de retour de la fonction", "Infer_parameter_types_from_usage_95012": "Déduire les types des paramètres à partir de l'utilisation", "Infer_this_type_of_0_from_usage_95080": "<PERSON><PERSON><PERSON><PERSON> le type 'this' de '{0}' à partir de l'utilisation", "Infer_type_of_0_from_usage_95011": "Déduire le type de '{0}' à partir de l'utilisation", "Initialize_property_0_in_the_constructor_90020": "Initialiser la propriété '{0}' dans le constructeur", "Initialize_static_property_0_90021": "Initialiser la propriété statique '{0}'", "Initializer_for_property_0_2811": "Initialiseur de la propriété '{0}'", "Initializer_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2301": "L'initialiseur de la variable membre d'instance '{0}' ne peut pas référencer l'identificateur '{1}' déclaré dans le constructeur.", "Initializer_provides_no_value_for_this_binding_element_and_the_binding_element_has_no_default_value_2525": "L'initialiseur ne fournit aucune valeur pour cet élément de liaison, et ce dernier n'a pas de valeur par défaut.", "Initializers_are_not_allowed_in_ambient_contexts_1039": "Les initialiseurs ne sont pas autorisés dans les contextes ambiants.", "Initializes_a_TypeScript_project_and_creates_a_tsconfig_json_file_6070": "Initialise un projet TypeScript et crée un fichier tsconfig.json.", "Insert_command_line_options_and_files_from_a_file_6030": "Insérer les options de ligne de commande et les fichiers à partir d'un fichier texte.", "Install_0_95014": "Installer '{0}'", "Install_all_missing_types_packages_95033": "Installer tous les packages de types manquants", "Interface_0_cannot_simultaneously_extend_types_1_and_2_2320": "L'interface '{0}' ne peut pas étendre simultanément les types '{1}' et '{2}'.", "Interface_0_incorrectly_extends_interface_1_2430": "L'interface '{0}' étend de manière incorrecte l'interface '{1}'.", "Interface_declaration_cannot_have_implements_clause_1176": "Une déclaration d'interface ne peut pas avoir de clause 'implements'.", "Interface_must_be_given_a_name_1438": "Un nom doit être attribué à l’interface.", "Interface_name_cannot_be_0_2427": "Le nom de l'interface ne peut pas être '{0}'.", "Interop_Constraints_6252": "Contraintes d’interopérabilité", "Interpret_optional_property_types_as_written_rather_than_adding_undefined_6243": "Interpréter les types de propriétés facultatifs comme écrits, plutôt que d’ajouter « undefined ».", "Invalid_character_1127": "Caractère non valide.", "Invalid_import_specifier_0_has_no_possible_resolutions_6272": "Le spécificateur d’importation non valide « {0} » n’a aucune résolution possible.", "Invalid_module_name_in_augmentation_Module_0_resolves_to_an_untyped_module_at_1_which_cannot_be_augm_2665": "Nom de module non valide dans l'augmentation. Le module '{0}' est résolu en module non typé à l'emplacement '{1}', ce qui empêche toute augmentation.", "Invalid_module_name_in_augmentation_module_0_cannot_be_found_2664": "Nom de module non valide dans l'augmentation. Le module '{0}' est introuvable.", "Invalid_optional_chain_from_new_expression_Did_you_mean_to_call_0_1209": "Chaîne facultative non valide à partir de la nouvelle expression. Voulez-vous appeler '{0}()' ?", "Invalid_reference_directive_syntax_1084": "Syntaxe de directive 'reference' non valide.", "Invalid_use_of_0_It_cannot_be_used_inside_a_class_static_block_18039": "Utilisation non valide de « {0} ». Il ne peut pas être utilisé à l’intérieur d’un bloc statique de classe.", "Invalid_use_of_0_Modules_are_automatically_in_strict_mode_1215": "Utilisation non valide de '{0}'. Les modules sont automatiquement en mode strict.", "Invalid_use_of_0_in_strict_mode_1100": "Utilisation non valide de '{0}' en mode strict.", "Invalid_value_for_jsxFactory_0_is_not_a_valid_identifier_or_qualified_name_5067": "Valeur non valide pour 'jsxFactory'. '{0}' n'est pas un identificateur valide ou un nom qualifié.", "Invalid_value_for_jsxFragmentFactory_0_is_not_a_valid_identifier_or_qualified_name_18035": "Valeur non valide pour 'jsxFragmentFactory'. '{0}' n'est pas un identificateur valide ou un nom qualifié.", "Invalid_value_for_reactNamespace_0_is_not_a_valid_identifier_5059": "Valeur non valide pour '--reactNamespace'. '{0}' n'est pas un identificateur valide.", "It_is_likely_that_you_are_missing_a_comma_to_separate_these_two_template_expressions_They_form_a_tag_2796": "Il manque probablement une virgule pour séparer ces deux expressions de modèle. Elles forment une expression de modèle étiquetée qui ne peut pas être appelée.", "Its_element_type_0_is_not_a_valid_JSX_element_2789": "Son type d'élément '{0}' n'est pas un élément JSX valide.", "Its_instance_type_0_is_not_a_valid_JSX_element_2788": "Son type d'instance '{0}' n'est pas un élément JSX valide.", "Its_return_type_0_is_not_a_valid_JSX_element_2787": "Son type de retour '{0}' n'est pas un élément JSX valide.", "JSDoc_0_1_does_not_match_the_extends_2_clause_8023": "La balise JSDoc '@{0} {1}' ne correspond pas à la clause 'extends {2}'.", "JSDoc_0_is_not_attached_to_a_class_8022": "La balise JSDoc '@{0}' n'est pas attachée à une classe.", "JSDoc_may_only_appear_in_the_last_parameter_of_a_signature_8028": "JSDoc '...' peut apparaître uniquement dans le dernier paramètre d'une signature.", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_8024": "La balise JSDoc '@param' se nomme '{0}', mais il n'existe aucun paramètre portant ce nom.", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_It_would_match_arguments_if_it_h_8029": "La balise JSDoc '@param' se nomme '{0}', mais il n'existe aucun paramètre portant ce nom. Elle doit correspondre à 'arguments', si elle est de type tableau.", "JSDoc_typedef_tag_should_either_have_a_type_annotation_or_be_followed_by_property_or_member_tags_8021": "La balise JSDoc '@typedef' doit avoir une annotation de type ou être suivie des balises '@property' ou '@member'.", "JSDoc_types_can_only_be_used_inside_documentation_comments_8020": "Les types JSDoc peuvent uniquement être utilisés dans les commentaires de la documentation.", "JSDoc_types_may_be_moved_to_TypeScript_types_80004": "Les types JSDoc peuvent être déplacés vers les types TypeScript.", "JSX_attributes_must_only_be_assigned_a_non_empty_expression_17000": "Les attributs JSX doivent uniquement être attribués à une 'expression' non vide.", "JSX_element_0_has_no_corresponding_closing_tag_17008": "L'élément JSX '{0}' n'a pas de balise de fermeture correspondante.", "JSX_element_class_does_not_support_attributes_because_it_does_not_have_a_0_property_2607": "La classe de l'élément JSX ne prend pas en charge les attributs, car elle n'a pas de propriété '{0}'.", "JSX_element_implicitly_has_type_any_because_no_interface_JSX_0_exists_7026": "L'élément JSX a implicitement le type 'any', car il n'existe aucune interface 'JSX.{0}'.", "JSX_element_implicitly_has_type_any_because_the_global_type_JSX_Element_does_not_exist_2602": "L'élément JSX a implicitement le type 'any', car le type global 'JSX.Element' n'existe pas.", "JSX_element_type_0_does_not_have_any_construct_or_call_signatures_2604": "Le type '{0}' de l'élément JSX n'a pas de signatures de construction ou d'appel.", "JSX_elements_cannot_have_multiple_attributes_with_the_same_name_17001": "Les éléments JSX ne peuvent pas avoir plusieurs attributs du même nom.", "JSX_expressions_may_not_use_the_comma_operator_Did_you_mean_to_write_an_array_18007": "Les expressions JSX ne peuvent pas utiliser l'opérateur virgule. Est-ce que vous avez voulu écrire un tableau ?", "JSX_expressions_must_have_one_parent_element_2657": "Les expressions JSX doivent avoir un élément parent.", "JSX_fragment_has_no_corresponding_closing_tag_17014": "Le fragment JSX n'a pas de balise de fermeture correspondante.", "JSX_property_access_expressions_cannot_include_JSX_namespace_names_2633": "Les expressions d'accès aux propriétés JSX ne peuvent pas inclure de noms d'espaces de noms JSX", "JSX_spread_child_must_be_an_array_type_2609": "L'enfant spread JSX doit être un type de tableau.", "JavaScript_Support_6247": "Prise en charge de JavaScript", "Jump_target_cannot_cross_function_boundary_1107": "La cible du saut ne peut pas traverser une limite de fonction.", "KIND_6034": "GENRE", "Keywords_cannot_contain_escape_characters_1260": "Les mots clés ne peuvent pas contenir de caractères d'échappement.", "LOCATION_6037": "EMPLACEMENT", "Language_and_Environment_6254": "Langage et Environnement", "Left_side_of_comma_operator_is_unused_and_has_no_side_effects_2695": "Le côté gauche de l'opérateur virgule n'est pas utilisé, et n'a aucun effet secondaire.", "Library_0_specified_in_compilerOptions_1422": "Bibliothèque '{0}' spécifiée dans compilerOptions", "Library_referenced_via_0_from_file_1_1405": "Bibliothèque référencée via '{0}' à partir du fichier '{1}'", "Line_break_not_permitted_here_1142": "Saut de ligne non autorisé ici.", "Line_terminator_not_permitted_before_arrow_1200": "Marque de fin de ligne non autorisée devant une flèche.", "List_of_file_name_suffixes_to_search_when_resolving_a_module_6931": "Liste des suffixes de nom de fichier à rechercher lors de la résolution d’un module.", "List_of_folders_to_include_type_definitions_from_6161": "Liste des dossiers à partir desquels inclure les définitions de type.", "List_of_root_folders_whose_combined_content_represents_the_structure_of_the_project_at_runtime_6168": "Liste des dossiers racines dont le contenu combiné représente la structure du projet au moment de l'exécution.", "Loading_0_from_the_root_dir_1_candidate_location_2_6109": "Chargement de '{0}' à partir du répertoire racine '{1}', emplacement candidat '{2}'.", "Loading_module_0_from_node_modules_folder_target_file_type_1_6098": "Chargement du module '{0}' à partir du dossier 'node_modules'. Type du fichier cible '{1}'.", "Loading_module_as_file_Slash_folder_candidate_module_location_0_target_file_type_1_6095": "Chargement du module en tant que fichier/dossier. Emplacement du module candidat '{0}'. Type de fichier cible '{1}'.", "Locale_must_be_of_the_form_language_or_language_territory_For_example_0_or_1_6048": "Les paramètres régionaux doivent être sous la forme <langue> ou <langue>-<territoire>. Par exemple, '{0}' ou '{1}'.", "Log_paths_used_during_the_moduleResolution_process_6706": "Chemins d’accès de journal utilisés pendant le processus « moduleResolution ».", "Longest_matching_prefix_for_0_is_1_6108": "Le préfixe correspondant le plus long pour '{0}' est '{1}'.", "Looking_up_in_node_modules_folder_initial_location_0_6125": "Recherche dans le dossier 'node_modules', emplacement initial '{0}'.", "Make_all_super_calls_the_first_statement_in_their_constructor_95036": "Faire de tous les appels 'super()' la première instruction dans leur constructeur", "Make_keyof_only_return_strings_instead_of_string_numbers_or_symbols_Legacy_option_6650": "Faites en sorte que keyof retourne uniquement des chaînes au lieu de chaînes, de nombres ou de symboles. Option héritée.", "Make_super_call_the_first_statement_in_the_constructor_90002": "Faire de l'appel à 'super()' la première instruction du constructeur", "Mapped_object_type_implicitly_has_an_any_template_type_7039": "Le type d'objet mappé a implicitement un type de modèle 'any'.", "Matched_0_condition_1_6403": "Condition '{0}' correspondant à '{1}'.", "Matched_by_default_include_pattern_Asterisk_Asterisk_Slash_Asterisk_1457": "Mise en correspondance par défaut du modèle include '**/*'", "Matched_by_include_pattern_0_in_1_1407": "Correspond au modèle include '{0}' dans '{1}'", "Member_0_implicitly_has_an_1_type_7008": "Le membre '{0}' possède implicitement un type '{1}'.", "Member_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7045": "Le membre '{0}' a implicitement un type '{1}', mais il est possible de déduire un meilleur type à partir de l'utilisation.", "Merge_conflict_marker_encountered_1185": "Marqueur de conflit de fusion rencontré.", "Merged_declaration_0_cannot_include_a_default_export_declaration_Consider_adding_a_separate_export_d_2652": "La déclaration fusionnée '{0}' ne peut pas inclure de déclaration d'exportation par défaut. Ajoutez plutôt une déclaration 'export default {0}' distincte.", "Meta_property_0_is_only_allowed_in_the_body_of_a_function_declaration_function_expression_or_constru_17013": "La méta-propriété '{0}' n'est autorisée que dans le corps d'une déclaration de fonction, d'une expression de fonction ou d'un constructeur.", "Method_0_cannot_have_an_implementation_because_it_is_marked_abstract_1245": "La méthode '{0}' ne peut pas avoir d'implémentation, car elle est marquée comme étant abstraite.", "Method_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4101": "La méthode '{0}' de l'interface exportée comporte ou utilise le nom '{1}' du module privé '{2}'.", "Method_0_of_exported_interface_has_or_is_using_private_name_1_4102": "La méthode '{0}' de l'interface exportée comporte ou utilise le nom privé '{1}'.", "Method_not_implemented_95158": "Méthode non implémentée.", "Modifiers_cannot_appear_here_1184": "Les modificateurs ne peuvent pas apparaître ici.", "Module_0_can_only_be_default_imported_using_the_1_flag_1259": "Le module '{0}' peut uniquement être importé par défaut à l'aide de l'indicateur '{1}'", "Module_0_cannot_be_imported_using_this_construct_The_specifier_only_resolves_to_an_ES_module_which_c_1471": "Le module '{0}' ne peut pas être importé à l'aide de cette construction. Le spécificateur se résout uniquement en un module ES, qui ne peut pas être importé avec 'require'. Utilisez plutôt une importation ECMAScript.", "Module_0_declares_1_locally_but_it_is_exported_as_2_2460": "Le module '{0}' déclare '{1}' localement, mais il est exporté en tant que '{2}'.", "Module_0_declares_1_locally_but_it_is_not_exported_2459": "Le module '{0}' déclare '{1}' localement, mais il n'est pas exporté.", "Module_0_does_not_refer_to_a_type_but_is_used_as_a_type_here_Did_you_mean_typeof_import_0_1340": "Le module '{0}' ne fait pas référence à un type, mais il est utilisé ici en tant que type. Est-ce que vous avez voulu utiliser 'typeof import('{0}')' ?", "Module_0_does_not_refer_to_a_value_but_is_used_as_a_value_here_1339": "Le module '{0}' ne fait pas référence à une valeur, mais est utilisé en tant que valeur ici.", "Module_0_has_already_exported_a_member_named_1_Consider_explicitly_re_exporting_to_resolve_the_ambig_2308": "Le module {0} a déjà exporté un membre nommé '{1}'. Effectuez une réexportation explicite pour lever l'ambiguïté.", "Module_0_has_no_default_export_1192": "Le module '{0}' n'a pas d'exportation par défaut.", "Module_0_has_no_default_export_Did_you_mean_to_use_import_1_from_0_instead_2613": "Le module '{0}' n'a aucune exportation par défaut. Est-ce que vous avez voulu utiliser 'import { {1} } from {0}' à la place ?", "Module_0_has_no_exported_member_1_2305": "Le module '{0}' n'a aucun membre exporté '{1}'.", "Module_0_has_no_exported_member_1_Did_you_mean_to_use_import_1_from_0_instead_2614": "Le module '{0}' n'a aucun membre exporté '{1}'. Est-ce que vous avez voulu utiliser 'import {1} from {0}' à la place ?", "Module_0_is_hidden_by_a_local_declaration_with_the_same_name_2437": "Le module '{0}' est masqué par une déclaration locale portant le même nom.", "Module_0_uses_export_and_cannot_be_used_with_export_Asterisk_2498": "Le module '{0}' utilise 'export =' et ne peut pas être utilisé avec 'export *'.", "Module_0_was_resolved_as_ambient_module_declared_in_1_since_this_file_was_not_modified_6145": "Le module '{0}' a été résolu en tant que module ambiant déclaré dans '{1}', car ce fichier n'a pas été modifié.", "Module_0_was_resolved_as_locally_declared_ambient_module_in_file_1_6144": "Le module '{0}' a été résolu en tant que module ambiant déclaré localement dans le fichier '{1}'.", "Module_0_was_resolved_to_1_but_jsx_is_not_set_6142": "Le module '{0}' a été résolu en '{1}' mais '--jsx' n'est pas défini.", "Module_0_was_resolved_to_1_but_resolveJsonModule_is_not_used_7042": "Le module '{0}' a été résolu en '{1}' mais '--resolveJsonModule' n'est pas utilisé.", "Module_declaration_names_may_only_use_or_quoted_strings_1443": "Les noms de déclaration de module ne peuvent utiliser que des chaînes entre guillemets.", "Module_name_0_matched_pattern_1_6092": "Nom de module '{0}', modèle correspondant '{1}'.", "Module_name_0_was_not_resolved_6090": "======== Le nom de module '{0}' n'a pas été résolu. ========", "Module_name_0_was_successfully_resolved_to_1_6089": "======== Le nom de module '{0}' a été correctement résolu en '{1}'. ========", "Module_name_0_was_successfully_resolved_to_1_with_Package_ID_2_6218": "======== Le nom de module '{0}' a été correctement résolu en '{1}' avec l'ID de package '{2}'. ========", "Module_resolution_kind_is_not_specified_using_0_6088": "Le genre de résolution de module n'est pas spécifié. Utilisation de '{0}'.", "Module_resolution_using_rootDirs_has_failed_6111": "Échec de la résolution de module à l'aide de 'rootDirs'.", "Modules_6244": "<PERSON><PERSON><PERSON>", "Move_labeled_tuple_element_modifiers_to_labels_95117": "<PERSON><PERSON><PERSON>r les modificateurs d'élément de tuple étiqueté vers les étiquettes", "Move_to_a_new_file_95049": "<PERSON><PERSON><PERSON>r vers un nouveau fichier", "Multiple_consecutive_numeric_separators_are_not_permitted_6189": "Les séparateurs numériques consécutifs multiples ne sont pas autorisés.", "Multiple_constructor_implementations_are_not_allowed_2392": "Les implémentations de plusieurs constructeurs ne sont pas autorisées.", "NEWLINE_6061": "NOUVELLE LIGNE", "Name_is_not_valid_95136": "Le nom n'est pas valide", "Named_property_0_of_types_1_and_2_are_not_identical_2319": "La propriété nommée '{0}' des types '{1}' et '{2}' n'est pas identique.", "Namespace_0_has_no_exported_member_1_2694": "L'espace de noms '{0}' n'a aucun membre exporté '{1}'.", "Namespace_must_be_given_a_name_1437": "Un nom doit être attribué à l’espace de noms.", "Namespace_name_cannot_be_0_2819": "L’espace de noms ne peut pas être «{0}».", "No_base_constructor_has_the_specified_number_of_type_arguments_2508": "Aucun constructeur de base n'a le nombre spécifié d'arguments de type.", "No_constituent_of_type_0_is_callable_2755": "Aucun constituant de type '{0}' ne peut être appelé.", "No_constituent_of_type_0_is_constructable_2759": "Aucun constituant de type '{0}' ne peut être construit.", "No_index_signature_with_a_parameter_of_type_0_was_found_on_type_1_7054": "Aucune signature d'index avec un paramètre de type '{0}' n'a été localisée sur le type '{1}'.", "No_inputs_were_found_in_config_file_0_Specified_include_paths_were_1_and_exclude_paths_were_2_18003": "Aucune entrée dans le fichier config '{0}'. Les chemins 'include' spécifiés étaient '{1}' et les chemins 'exclude' étaient '{2}'.", "No_longer_supported_In_early_versions_manually_set_the_text_encoding_for_reading_files_6608": "Plus prise en charge. Dans les premières versions, définissez manuellement l’encodage de texte pour la lecture des fichiers.", "No_overload_expects_0_arguments_but_overloads_do_exist_that_expect_either_1_or_2_arguments_2575": "Aucune surcharge n'attend {0} arguments, mais il existe des surcharges qui attendent {1} ou {2} arguments.", "No_overload_expects_0_type_arguments_but_overloads_do_exist_that_expect_either_1_or_2_type_arguments_2743": "Aucune surcharge n'attend {0} arguments de type, mais il existe des surcharges qui attendent {1} ou {2} arguments de type.", "No_overload_matches_this_call_2769": "Aucune surcharge ne correspond à cet appel.", "No_type_could_be_extracted_from_this_type_node_95134": "Aucun type n'a pu être extrait de ce nœud de type", "No_value_exists_in_scope_for_the_shorthand_property_0_Either_declare_one_or_provide_an_initializer_18004": "Il n'existe aucune valeur dans l'étendue de la propriété raccourcie '{0}'. V<PERSON> devez en déclarez une, ou fournir un initialiseur.", "Non_abstract_class_0_does_not_implement_inherited_abstract_member_1_from_class_2_2515": "La classe non abstraite '{0}' n'implémente pas le membre abstrait '{1}' hérité de la classe '{2}'.", "Non_abstract_class_expression_does_not_implement_inherited_abstract_member_0_from_class_1_2653": "L'expression de classe non abstraite '{0}' n'implémente pas le membre abstrait hérité '{0}' de la classe '{1}'.", "Non_null_assertions_can_only_be_used_in_TypeScript_files_8013": "Les assertions non null peuvent uniquement être utilisées dans les fichiers TypeScript.", "Non_relative_paths_are_not_allowed_when_baseUrl_is_not_set_Did_you_forget_a_leading_Slash_5090": "Les chemins non relatifs ne sont pas autorisés quand 'baseUrl' n'est pas défini. Avez-vous oublié './' au début ?", "Non_simple_parameter_declared_here_1348": "Paramètre non simple déclaré ici.", "Not_all_code_paths_return_a_value_7030": "Les chemins du code ne retournent pas tous une valeur.", "Not_all_constituents_of_type_0_are_callable_2756": "Tous les constituants de type '{0}' ne peuvent pas être appelés.", "Not_all_constituents_of_type_0_are_constructable_2760": "Tous les constituants de type '{0}' ne peuvent pas être construits.", "Numeric_literals_with_absolute_values_equal_to_2_53_or_greater_are_too_large_to_be_represented_accur_80008": "Les littéraux numériques ayant des valeurs absolues égales ou supérieures à 2^53 sont trop grands pour être représentés avec précision sous forme d'entiers.", "Numeric_separators_are_not_allowed_here_6188": "Les séparateurs numériques ne sont pas autorisés ici.", "Object_is_of_type_unknown_2571": "L'objet est de type 'unknown'.", "Object_is_possibly_null_2531": "L'objet a peut-être la valeur 'null'.", "Object_is_possibly_null_or_undefined_2533": "L'objet a peut-être la valeur 'null' ou 'undefined'.", "Object_is_possibly_undefined_2532": "L'objet a peut-être la valeur 'undefined'.", "Object_literal_may_only_specify_known_properties_and_0_does_not_exist_in_type_1_2353": "Un littéral d'objet peut uniquement spécifier des propriétés connues, et '{0}' n'existe pas dans le type '{1}'.", "Object_literal_may_only_specify_known_properties_but_0_does_not_exist_in_type_1_Did_you_mean_to_writ_2561": "Un littéral d'objet peut uniquement spécifier des propriétés connues, mais '{0}' n'existe pas dans le type '{1}'. Est-ce que vous avez voulu écrire '{2}' ?", "Object_literal_s_property_0_implicitly_has_an_1_type_7018": "La propriété '{0}' du littéral d'objet possède implicitement un type '{1}'.", "Octal_digit_expected_1178": "Chiffre octal attendu.", "Octal_literal_types_must_use_ES2015_syntax_Use_the_syntax_0_8017": "Les types de littéral octal doivent utiliser la syntaxe ES2015. Utilisez la syntaxe '{0}'.", "Octal_literals_are_not_allowed_in_enums_members_initializer_Use_the_syntax_0_8018": "Les littéraux octaux ne sont pas autorisés dans l'initialiseur des membres d'enums. Utilisez la syntaxe '{0}'.", "Octal_literals_are_not_allowed_in_strict_mode_1121": "Les littéraux octaux ne sont pas autorisés en mode strict.", "Octal_literals_are_not_available_when_targeting_ECMAScript_5_and_higher_Use_the_syntax_0_1085": "Les littéraux octaux ne sont pas disponibles lorsque vous ciblez ECMAScript 5 et ultérieur. Utilisez la syntaxe '{0}'.", "Only_a_single_variable_declaration_is_allowed_in_a_for_in_statement_1091": "Une seule déclaration de variable est autorisée dans une instruction 'for...in'.", "Only_a_single_variable_declaration_is_allowed_in_a_for_of_statement_1188": "Seule une déclaration de variable unique est autorisée dans une instruction 'for...of'.", "Only_a_void_function_can_be_called_with_the_new_keyword_2350": "Seule une fonction void peut être appelée avec le mot clé 'new'.", "Only_ambient_modules_can_use_quoted_names_1035": "Seuls les modules ambiants peuvent utiliser des noms entre guillemets.", "Only_amd_and_system_modules_are_supported_alongside_0_6082": "Seuls les modules 'amd' et 'system' sont pris en charge avec --{0}.", "Only_emit_d_ts_declaration_files_6014": "Émettez uniquement les fichiers de déclaration '.d.ts'.", "Only_named_exports_may_use_export_type_1383": "Seules les exportations nommées peuvent utiliser 'export type'.", "Only_numeric_enums_can_have_computed_members_but_this_expression_has_type_0_If_you_do_not_need_exhau_18033": "Seules les enums numériques peuvent avoir des membres calculés, mais cette expression a le type '{0}'. Si vous n'avez pas besoin de contrôles d'exhaustivité, utilisez un littéral d'objet à la place.", "Only_output_d_ts_files_and_not_JavaScript_files_6623": "Sortie uniquement des fichiers d.ts et non des fichiers JavaScript.", "Only_public_and_protected_methods_of_the_base_class_are_accessible_via_the_super_keyword_2340": "Seules les méthodes publiques et protégées de la classe de base sont accessibles par le biais du mot clé 'super'.", "Operator_0_cannot_be_applied_to_type_1_2736": "Impossible d'appliquer l'opérateur '{0}' au type '{1}'.", "Operator_0_cannot_be_applied_to_types_1_and_2_2365": "Impossible d'appliquer l'opérateur '{0}' aux types '{1}' et '{2}'.", "Opt_a_project_out_of_multi_project_reference_checking_when_editing_6619": "Choisir un projet en dehors de la vérification des références multiprojets lors de l’édition.", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_false_or_null_on_command_line_6230": "V<PERSON> pouvez spécifier l'option '{0}' uniquement dans le fichier 'tsconfig.json', ou lui affecter la valeur 'false' ou 'null' sur la ligne de commande.", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_null_on_command_line_6064": "V<PERSON> pouvez spécifier l'option '{0}' uniquement dans le fichier 'tsconfig.json', ou lui affecter la valeur 'null' sur la ligne de commande.", "Option_0_can_only_be_used_when_either_option_inlineSourceMap_or_option_sourceMap_is_provided_5051": "L'option '{0}' peut être utilisée uniquement quand l'option '--inlineSourceMap' ou l'option '--sourceMap' est spécifiée.", "Option_0_cannot_be_specified_when_option_jsx_is_1_5089": "Impossible de spécifier l'option '{0}' quand l'option 'jsx' a la valeur '{1}'.", "Option_0_cannot_be_specified_when_option_target_is_ES3_5048": "Impossible de spécifier l'option '{0}' quand l'option 'target' est 'ES3'.", "Option_0_cannot_be_specified_with_option_1_5053": "Impossible de spécifier l'option '{0}' avec l'option '{1}'.", "Option_0_cannot_be_specified_without_specifying_option_1_5052": "Impossible de spécifier l'option '{0}' sans spécifier l'option '{1}'.", "Option_0_cannot_be_specified_without_specifying_option_1_or_option_2_5069": "Impossible de spécifier l'option '{0}' sans spécifier l'option '{1}' ou l'option '{2}'.", "Option_build_must_be_the_first_command_line_argument_6369": "L'option '--build' doit être le premier argument de ligne de commande.", "Option_incremental_can_only_be_specified_using_tsconfig_emitting_to_single_file_or_when_option_tsBui_5074": "L'option '--incremental' peut uniquement être spécifiée à l'aide de tsconfig, en cas d'émission vers un seul fichier ou quand l'option '--tsBuildInfoFile' est spécifiée.", "Option_isolatedModules_can_only_be_used_when_either_option_module_is_provided_or_option_target_is_ES_5047": "L'option 'isolatedModules' peut être utilisée seulement quand l'option '--module' est spécifiée, ou quand l'option 'target' a la valeur 'ES2015' ou une version supérieure.", "Option_preserveConstEnums_cannot_be_disabled_when_isolatedModules_is_enabled_5091": "L'option 'preserveConstEnums' ne peut pas être désactivée quand 'isolatedModules' est activé.", "Option_preserveValueImports_can_only_be_used_when_module_is_set_to_es2015_or_later_5095": "L’option « preserveValueImports » peut uniquement être utilisée quand « module » a la valeur « es2015 » ou une version ultérieure.", "Option_project_cannot_be_mixed_with_source_files_on_a_command_line_5042": "Impossible d'associer l'option 'project' à des fichiers sources sur une ligne de commande.", "Option_resolveJsonModule_can_only_be_specified_when_module_code_generation_is_commonjs_amd_es2015_or_5071": "L'option '--resolveJsonModule' peut uniquement être spécifiée quand la génération du code de module est 'commonjs', 'amd', 'es2015' ou 'esNext'.", "Option_resolveJsonModule_cannot_be_specified_without_node_module_resolution_strategy_5070": "Impossible de spécifier l'option '--resolveJsonModule' sans la stratégie de résolution de module 'node'.", "Options_0_and_1_cannot_be_combined_6370": "Impossible de combiner les options '{0}' et '{1}'.", "Options_Colon_6027": "Options :", "Output_Formatting_6256": "Mise en forme de sortie", "Output_compiler_performance_information_after_building_6615": "Informations de performances du compilateur de sortie après la génération.", "Output_directory_for_generated_declaration_files_6166": "Répertoire de sortie pour les fichiers de déclaration générés.", "Output_file_0_from_project_1_does_not_exist_6309": "Le fichier de sortie '{0}' du projet '{1}' n'existe pas", "Output_file_0_has_not_been_built_from_source_file_1_6305": "Le fichier de sortie '{0}' n'a pas été créé à partir du fichier source '{1}'.", "Output_from_referenced_project_0_included_because_1_specified_1411": "Sortie du projet référencé '{0}' incluse, car '{1}' est spécifié", "Output_from_referenced_project_0_included_because_module_is_specified_as_none_1412": "Sortie du projet référencé '{0}' incluse, car '--module' est spécifié en tant que 'none'", "Output_more_detailed_compiler_performance_information_after_building_6632": "Affichez des informations plus détaillées sur les performances du compilateur après la génération.", "Overload_0_of_1_2_gave_the_following_error_2772": "La surcharge {0} sur {1}, '{2}', a généré l'erreur suivante.", "Overload_signatures_must_all_be_abstract_or_non_abstract_2512": "Les signatures de surcharge doivent toutes être abstraites ou non abstraites.", "Overload_signatures_must_all_be_ambient_or_non_ambient_2384": "Les signatures de surcharge doivent toutes être ambiantes ou non ambiantes.", "Overload_signatures_must_all_be_exported_or_non_exported_2383": "Les signatures de surcharge doivent toutes être exportées ou non exportées.", "Overload_signatures_must_all_be_optional_or_required_2386": "Les signatures de surcharge doivent toutes être facultatives ou requises.", "Overload_signatures_must_all_be_public_private_or_protected_2385": "Les signatures de surcharge doivent toutes être publiques, privées ou protégées.", "Parameter_0_cannot_reference_identifier_1_declared_after_it_2373": "Le paramètre '{0}' ne peut pas référencer l'identificateur '{1}' déclaré après lui.", "Parameter_0_cannot_reference_itself_2372": "Le paramètre '{0}' ne peut pas se référencer lui-même.", "Parameter_0_implicitly_has_an_1_type_7006": "Le paramètre '{0}' possède implicitement un type '{1}'.", "Parameter_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7044": "Le paramètre '{0}' a implicitement un type '{1}', mais il est possible de déduire un meilleur type à partir de l'utilisation.", "Parameter_0_is_not_in_the_same_position_as_parameter_1_1227": "Le paramètre '{0}' n'est pas à la même position que le paramètre '{1}'.", "Parameter_0_of_accessor_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4108": "Le paramètre '{0}' de l'accesseur comporte ou utilise le nom '{1}' du module externe '{2}' mais ne peut pas être nommé.", "Parameter_0_of_accessor_has_or_is_using_name_1_from_private_module_2_4107": "Le paramètre '{0}' de l'accesseur comporte ou utilise le nom '{1}' du module privé '{2}'.", "Parameter_0_of_accessor_has_or_is_using_private_name_1_4106": "Le paramètre '{0}' de l'accesseur comporte ou utilise le nom privé '{1}'.", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4066": "Le paramètre '{0}' de la signature d'appel de l'interface exportée possède ou utilise le nom '{1}' du module privé '{2}'.", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4067": "Le paramètre '{0}' de la signature d'appel de l'interface exportée possède ou utilise le nom privé '{1}'.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_can_4061": "Le paramètre '{0}' du constructeur de la classe exportée possède ou utilise le nom '{1}' du module externe {2}, mais il ne peut pas être nommé.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_private_module_2_4062": "Le paramètre '{0}' du constructeur de la classe exportée possède ou utilise le nom '{1}' du module privé '{2}'.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_private_name_1_4063": "Le paramètre '{0}' du constructeur de la classe exportée possède ou utilise le nom privé '{1}'.", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_name_1_from_private_mod_4064": "Le paramètre '{0}' de la signature de constructeur de l'interface exportée possède ou utilise le nom '{1}' du module privé '{2}'.", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4065": "Le paramètre '{0}' de la signature de constructeur de l'interface exportée possède ou utilise le nom privé '{1}'.", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4076": "Le paramètre '{0}' de la fonction exportée possède ou utilise le nom '{1}' du module externe {2}, mais il ne peut pas être nommé.", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_private_module_2_4077": "Le paramètre '{0}' de la fonction exportée possède ou utilise le nom '{1}' du module privé '{2}'.", "Parameter_0_of_exported_function_has_or_is_using_private_name_1_4078": "Le paramètre '{0}' de la fonction exportée possède ou utilise le nom privé '{1}'.", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4091": "Le paramètre '{0}' de la signature d'index de l'interface exportée a ou utilise le nom '{1}' du module privé '{2}'.", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_private_name_1_4092": "Le paramètre '{0}' de la signature d'index de l'interface exportée a ou utilise le nom privé '{1}'.", "Parameter_0_of_method_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4074": "Le paramètre '{0}' de la méthode de l'interface exportée possède ou utilise le nom '{1}' du module privé '{2}'.", "Parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4075": "Le paramètre '{0}' de la méthode de l'interface exportée possède ou utilise le nom privé '{1}'.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_c_4071": "Le paramètre '{0}' de la méthode publique de la classe exportée possède ou utilise le nom '{1}' du module externe {2}, mais il ne peut pas être nommé.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4072": "Le paramètre '{0}' de la méthode publique de la classe exportée possède ou utilise le nom '{1}' du module privé '{2}'.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4073": "Le paramètre '{0}' de la méthode publique de la classe exportée possède ou utilise le nom privé '{1}'.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_external_module__4068": "Le paramètre '{0}' de la méthode statique publique de la classe exportée possède ou utilise le nom '{1}' du module externe {2}, mais il ne peut pas être nommé.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4069": "Le paramètre '{0}' de la méthode statique publique de la classe exportée possède ou utilise le nom '{1}' du module privé '{2}'.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4070": "Le paramètre '{0}' de la méthode statique publique de la classe exportée possède ou utilise le nom privé '{1}'.", "Parameter_cannot_have_question_mark_and_initializer_1015": "Un paramètre ne peut pas contenir de point d'interrogation et d'initialiseur.", "Parameter_declaration_expected_1138": "Déclaration de paramètre attendue.", "Parameter_has_a_name_but_no_type_Did_you_mean_0_Colon_1_7051": "Le paramètre a un nom mais aucun type. Est-ce que vous avez voulu utiliser '{0} : {1}' ?", "Parameter_modifiers_can_only_be_used_in_TypeScript_files_8012": "Les modificateurs de paramètres peuvent uniquement être utilisées dans les fichiers TypeScript.", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4036": "Le type de paramètre du setter public '{0}' de la classe exportée porte ou utilise le nom '{1}' du module privé '{2}'.", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_private_name_1_4037": "Le type de paramètre du setter public '{0}' de la classe exportée porte ou utilise le nom privé '{1}'.", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_name_1_from_private_mod_4034": "Le type de paramètre du setter public '{0}' de la classe exportée porte ou utilise le nom '{1}' du module privé '{2}'.", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_private_name_1_4035": "Le type de paramètre du setter public '{0}' de la classe exportée porte ou utilise le nom privé '{1}'.", "Parse_in_strict_mode_and_emit_use_strict_for_each_source_file_6141": "Analyser en mode strict et émettre \"use strict\" pour chaque fichier source.", "Part_of_files_list_in_tsconfig_json_1409": "Partie de la liste 'files' dans tsconfig.json", "Pattern_0_can_have_at_most_one_Asterisk_character_5061": "Le modèle '{0}' ne peut avoir qu'un seul caractère '*' au maximum.", "Performance_timings_for_diagnostics_or_extendedDiagnostics_are_not_available_in_this_session_A_nativ_6386": "Les minutages de performances pour '--diagnostics' ou '--extendedDiagnostics' ne sont pas disponibles dans cette session. Une implémentation native de l'API de performances web est introuvable.", "Platform_specific_6912": "Spécifique à la plateforme", "Prefix_0_with_an_underscore_90025": "Faire précéder '{0}' d'un trait de soulignement", "Prefix_all_incorrect_property_declarations_with_declare_95095": "Faire commencer toutes les déclarations de propriété incorrectes par 'declare'", "Prefix_all_unused_declarations_with_where_possible_95025": "Préfixer toutes les déclarations inutilisées avec '_' si possible", "Prefix_with_declare_95094": "Faire commencer par 'declare'", "Preserve_unused_imported_values_in_the_JavaScript_output_that_would_otherwise_be_removed_1449": "Conservez les valeurs importées inutilisées dans la sortie JavaScript qui seraient normalement supprimées.", "Print_all_of_the_files_read_during_the_compilation_6653": "Imprimez tous les fichiers lus pendant la compilation.", "Print_files_read_during_the_compilation_including_why_it_was_included_6631": "Fichiers d’impression lus pendant la compilation, notamment la raison de l’inclusion.", "Print_names_of_files_and_the_reason_they_are_part_of_the_compilation_6505": "Affiche les noms des fichiers et la raison pour laquelle ils font partie de la compilation.", "Print_names_of_files_part_of_the_compilation_6155": "Imprimez les noms des fichiers faisant partie de la compilation.", "Print_names_of_files_that_are_part_of_the_compilation_and_then_stop_processing_6503": "Affichez les noms des fichiers qui font partie de la compilation, puis arrêtez le traitement.", "Print_names_of_generated_files_part_of_the_compilation_6154": "Imprimez les noms des fichiers générés faisant partie de la compilation.", "Print_the_compiler_s_version_6019": "Affichez la version du compilateur.", "Print_the_final_configuration_instead_of_building_1350": "Affichez la configuration finale au lieu d'effectuer la génération.", "Print_the_names_of_emitted_files_after_a_compilation_6652": "Imprimez les noms des fichiers émis après une compilation.", "Print_this_message_6017": "Imprimez ce message.", "Private_accessor_was_defined_without_a_getter_2806": "L'accesseur privé a été défini sans getter.", "Private_identifiers_are_not_allowed_in_variable_declarations_18029": "Les identificateurs privés ne sont pas autorisés dans les déclarations de variable.", "Private_identifiers_are_not_allowed_outside_class_bodies_18016": "Les identificateurs privés ne sont pas autorisés en dehors des corps de classe.", "Private_identifiers_are_only_allowed_in_class_bodies_and_may_only_be_used_as_part_of_a_class_member__1451": "Les identificateurs privés ne sont autorisés que dans les corps de classe et ne peuvent être utilisés que dans le cadre d’une déclaration de membre de classe, d’accès à une propriété ou de la partie gauche d’une expression ’in'", "Private_identifiers_are_only_available_when_targeting_ECMAScript_2015_and_higher_18028": "Les identificateurs privés sont disponibles uniquement durant le ciblage d'ECMAScript 2015 et version ultérieure.", "Private_identifiers_cannot_be_used_as_parameters_18009": "Les identificateurs privés ne peuvent pas être utilisés en tant que paramètres.", "Private_or_protected_member_0_cannot_be_accessed_on_a_type_parameter_4105": "Le membre privé ou protégé '{0}' n'est pas accessible sur un paramètre de type.", "Project_0_can_t_be_built_because_its_dependency_1_has_errors_6363": "Impossible de générer le projet '{0}' car sa dépendance '{1}' comporte des erreurs", "Project_0_can_t_be_built_because_its_dependency_1_was_not_built_6383": "Impossible de générer le projet '{0}', car sa dépendance '{1}' n'a pas été générée", "Project_0_is_being_forcibly_rebuilt_6388": "Le projet « {0} » est en cours de régénération forcée", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_some_of_the_changes_were_not_emitte_6399": "Le projet « {0} » est obsolète, car le fichier buildinfo « {1} » indique que certaines modifications n’ont pas été émises", "Project_0_is_out_of_date_because_its_dependency_1_is_out_of_date_6353": "Le projet '{0}' est obsolète car sa dépendance '{1}' est obsolète", "Project_0_is_out_of_date_because_output_1_is_older_than_input_2_6350": "Le projet '{0}' est obsolète car la sortie (« {1} ») est antérieure à l'entrée « {2} »", "Project_0_is_out_of_date_because_output_file_1_does_not_exist_6352": "Le projet '{0}' est obsolète car le fichier de sortie '{1}' n'existe pas", "Project_0_is_out_of_date_because_output_for_it_was_generated_with_version_1_that_differs_with_curren_6381": "Le projet '{0}' est obsolète, car sa sortie a été générée avec la version '{1}', qui diffère de la version actuelle '{2}'", "Project_0_is_out_of_date_because_output_of_its_dependency_1_has_changed_6372": "Le projet '{0}' est obsolète, car la sortie de sa dépendance '{1}' a changé", "Project_0_is_out_of_date_because_there_was_error_reading_file_1_6401": "Le projet « {0} » est obsolète car une erreur s'est produite lors de la lecture du fichier « {1} »", "Project_0_is_up_to_date_6361": "Le projet '{0}' est à jour", "Project_0_is_up_to_date_because_newest_input_1_is_older_than_output_2_6351": "Le projet « {0} » est à jour car l'entrée la plus récente (« {1} ») est antérieure à la sortie (« {2} »)", "Project_0_is_up_to_date_but_needs_to_update_timestamps_of_output_files_that_are_older_than_input_fil_6400": "Project « {0} » est à jour, mais doit mettre à jour les horodatages des fichiers de sortie plus anciens que les fichiers d’entrée", "Project_0_is_up_to_date_with_d_ts_files_from_its_dependencies_6354": "Le projet '{0}' est à jour avec les fichiers .d.ts de ses dépendances", "Project_references_may_not_form_a_circular_graph_Cycle_detected_Colon_0_6202": "Les références de projet ne peuvent pas former un graphe circulaire. Cycle détecté : {0}", "Projects_6255": "Projets", "Projects_in_this_build_Colon_0_6355": "Projets dans cette build : {0}", "Properties_with_the_accessor_modifier_are_only_available_when_targeting_ECMAScript_2015_and_higher_18045": "Les propriétés avec le modificateur 'accessor' ne sont disponibles que pour le ciblage d’ECMAScript 2015 et versions ultérieures.", "Property_0_cannot_have_an_initializer_because_it_is_marked_abstract_1267": "La propriété « {0} » ne peut pas avoir d’initialiseur, car elle est marquée comme abstraite.", "Property_0_comes_from_an_index_signature_so_it_must_be_accessed_with_0_4111": "La propriété '{0}' est issue d'une signature d'index, elle doit donc faire l'objet d'un accès avec ['{0}'].", "Property_0_does_not_exist_on_type_1_2339": "La propriété '{0}' n'existe pas sur le type '{1}'.", "Property_0_does_not_exist_on_type_1_Did_you_mean_2_2551": "La propriété '{0}' n'existe pas sur le type '{1}'. Est-ce qu'il ne s'agit pas plutôt de '{2}' ?", "Property_0_does_not_exist_on_type_1_Did_you_mean_to_access_the_static_member_2_instead_2576": "La propriété '{0}' n'existe pas sur le type '{1}'. Ne vouliez-vous pas plutôt accéder au membre statique '{2}' ?", "Property_0_does_not_exist_on_type_1_Do_you_need_to_change_your_target_library_Try_changing_the_lib_c_2550": "La propriété '{0}' n'existe pas sur le type '{1}'. Devez-vous changer votre bibliothèque cible ? Essayez de changer l'option de compilateur 'lib' en '{2}' ou une version ultérieure.", "Property_0_does_not_exist_on_type_1_Try_changing_the_lib_compiler_option_to_include_dom_2812": "La propriété '{0}' n’existe pas sur le type '{1}'. Essayez de modifier l’option de compileur 'lib' pour inclure 'dom'.", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_a_class_static_block_2817": "La propriété « {0} » n'a aucun initialiseur et n'est pas définitivement assignée dans un bloc statique de classe.", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_the_constructor_2564": "La propriété '{0}' n'a aucun initialiseur et n'est pas définitivement assignée dans le constructeur.", "Property_0_implicitly_has_type_any_because_its_get_accessor_lacks_a_return_type_annotation_7033": "La propriété '{0}' a implicitement le type 'any', car son accesseur get ne dispose pas d'une annotation de type de retour.", "Property_0_implicitly_has_type_any_because_its_set_accessor_lacks_a_parameter_type_annotation_7032": "La propriété '{0}' a implicitement le type 'any', car son accesseur set ne dispose pas d'une annotation de type de paramètre.", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_get_accessor_may_be_inferred_from_usage_7048": "La propriété '{0}' a implicitement le type 'any', mais il est possible de déduire un meilleur type pour son accesseur get à partir de l'utilisation.", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_set_accessor_may_be_inferred_from_usage_7049": "La propriété '{0}' a implicitement le type 'any', mais il est possible de déduire un meilleur type pour son accesseur set à partir de l'utilisation.", "Property_0_in_type_1_is_not_assignable_to_the_same_property_in_base_type_2_2416": "Impossible d'assigner la propriété '{0}' du type '{1}' à la même propriété du type de base '{2}'.", "Property_0_in_type_1_is_not_assignable_to_type_2_2603": "La propriété '{0}' du type '{1}' ne peut pas être assignée au type '{2}'.", "Property_0_in_type_1_refers_to_a_different_member_that_cannot_be_accessed_from_within_type_2_18015": "La propriété '{0}' du type '{1}' fait référence à un membre distinct, qui n'est pas accessible à partir du type '{2}'.", "Property_0_is_declared_but_its_value_is_never_read_6138": "La propriété '{0}' est déclarée mais sa valeur n'est jamais lue.", "Property_0_is_incompatible_with_index_signature_2530": "La propriété '{0}' est incompatible avec la signature d'index.", "Property_0_is_missing_in_type_1_2324": "La propriété '{0}' est manquante dans le type '{1}'.", "Property_0_is_missing_in_type_1_but_required_in_type_2_2741": "La propriété '{0}' est absente du type '{1}' mais obligatoire dans le type '{2}'.", "Property_0_is_not_accessible_outside_class_1_because_it_has_a_private_identifier_18013": "La propriété '{0}' n'est pas accessible en dehors de la classe '{1}', car elle a un identificateur privé.", "Property_0_is_optional_in_type_1_but_required_in_type_2_2327": "La propriété '{0}' est facultative dans le type '{1}', mais obligatoire dans le type '{2}'.", "Property_0_is_private_and_only_accessible_within_class_1_2341": "La propriété '{0}' est privée et uniquement accessible dans la classe '{1}'.", "Property_0_is_private_in_type_1_but_not_in_type_2_2325": "La propriété '{0}' est privée dans le type '{1}', mais pas dans le type '{2}'.", "Property_0_is_protected_and_only_accessible_through_an_instance_of_class_1_This_is_an_instance_of_cl_2446": "La propriété '{0}' est protégée et uniquement accessible via une instance de la classe '{1}'. Ceci est une instance de la classe '{2}'.", "Property_0_is_protected_and_only_accessible_within_class_1_and_its_subclasses_2445": "La propriété '{0}' est protégée et uniquement accessible dans la classe '{1}' et ses sous-classes.", "Property_0_is_protected_but_type_1_is_not_a_class_derived_from_2_2443": "La propriété '{0}' est protégée, mais le type '{1}' n'est pas une classe dérivée de '{2}'.", "Property_0_is_protected_in_type_1_but_public_in_type_2_2444": "La propriété '{0}' est protégée dans le type '{1}', mais publique dans le type '{2}'.", "Property_0_is_used_before_being_assigned_2565": "La propriété '{0}' est utilisée avant d'être assignée.", "Property_0_is_used_before_its_initialization_2729": "La propriété '{0}' est utilisée avant son initialisation.", "Property_0_may_not_exist_on_type_1_Did_you_mean_2_2568": "La propriété «{0}» n'existe pas sur le type «{1}». Est-ce qu'il ne s'agit pas plutôt de «{2}»?", "Property_0_of_JSX_spread_attribute_is_not_assignable_to_target_property_2606": "Impossible d'assigner la propriété '{0}' de l'attribut spread JSX à la propriété cible.", "Property_0_of_exported_class_expression_may_not_be_private_or_protected_4094": "La propriété '{0}' de l'expression de classe exportée ne peut pas être privée ou protégée.", "Property_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4032": "La propriété '{0}' de l'interface exportée possède ou utilise le nom '{1}' du module privé '{2}'.", "Property_0_of_exported_interface_has_or_is_using_private_name_1_4033": "La propriété '{0}' de l'interface exportée possède ou utilise le nom privé '{1}'.", "Property_0_of_type_1_is_not_assignable_to_2_index_type_3_2411": "La propriété « {0} » de type « {1} » ne peut pas être attribuée au type d’index « {2} », « {3} ».", "Property_0_was_also_declared_here_2733": "La propriété '{0}' a également été déclarée ici.", "Property_0_will_overwrite_the_base_property_in_1_If_this_is_intentional_add_an_initializer_Otherwise_2612": "La propriété '{0}' va remplacer la propriété de base dans '{1}'. Si cela est intentionnel, ajoutez un initialiseur. Sinon, ajoutez un modificateur 'declare', ou supprimez la déclaration redondante.", "Property_assignment_expected_1136": "Assignation de propriété attendue.", "Property_destructuring_pattern_expected_1180": "Modèle de déstructuration de propriété attendu.", "Property_or_signature_expected_1131": "<PERSON><PERSON><PERSON><PERSON><PERSON> ou signature attendue.", "Property_value_can_only_be_string_literal_numeric_literal_true_false_null_object_literal_or_array_li_1328": "La valeur de la propriété peut être uniquement un littéral de chaîne, un littéral numérique, 'true', 'false', 'null', un littéral d'objet ou un littéral de tableau.", "Provide_full_support_for_iterables_in_for_of_spread_and_destructuring_when_targeting_ES5_or_ES3_6179": "Fournissez une prise en charge complète des éléments pouvant faire l'objet d'une itération dans 'for-of', de l'opérateur spread et de la déstructuration durant le ciblage d''ES5' ou 'ES3'.", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4098": "La méthode publique '{0}' de la classe exportée comporte ou utilise le nom '{1}' du module externe {2} mais ne peut pas être nommée.", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4099": "La méthode publique '{0}' de la classe exportée comporte ou utilise le nom '{1}' du module privé '{2}'.", "Public_method_0_of_exported_class_has_or_is_using_private_name_1_4100": "La méthode publique '{0}' de la classe exportée comporte ou utilise le nom privé '{1}'.", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_name_4029": "La propriété publique '{0}' de la classe exportée possède ou utilise le nom '{1}' du module externe {2}, mais elle ne peut pas être nommée.", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4030": "La propriété publique '{0}' de la classe exportée possède ou utilise le nom '{1}' du module privé '{2}'.", "Public_property_0_of_exported_class_has_or_is_using_private_name_1_4031": "La propriété publique '{0}' de la classe exportée possède ou utilise le type privé '{1}'.", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_4095": "La méthode statique publique '{0}' de la classe exportée comporte ou utilise le nom '{1}' du module externe {2} mais ne peut pas être nommée.", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4096": "La méthode statique publique '{0}' de la classe exportée comporte ou utilise le nom '{1}' du module privé '{2}'.", "Public_static_method_0_of_exported_class_has_or_is_using_private_name_1_4097": "La méthode statique publique '{0}' de la classe exportée comporte ou utilise le nom privé '{1}'.", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot__4026": "La propriété statique publique '{0}' de la classe exportée possède ou utilise le nom '{1}' du module externe {2}, mais elle ne peut pas être nommée.", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4027": "La propriété statique publique '{0}' de la classe exportée possède ou utilise le nom '{1}' du module privé '{2}'.", "Public_static_property_0_of_exported_class_has_or_is_using_private_name_1_4028": "La propriété statique publique '{0}' de la classe exportée possède ou utilise le type privé '{1}'.", "Qualified_name_0_is_not_allowed_without_a_leading_param_object_1_8032": "Le nom qualifié '{0}' n'est pas autorisé si '@param {object} {1}' n'est pas placé au début.", "Raise_an_error_when_a_function_parameter_isn_t_read_6676": "<PERSON><PERSON><PERSON><PERSON>er une erreur quand un paramètre de fonction n’est pas lu.", "Raise_error_on_expressions_and_declarations_with_an_implied_any_type_6052": "Lever une erreur sur les expressions et les déclarations ayant un type 'any' implicite.", "Raise_error_on_this_expressions_with_an_implied_any_type_6115": "Déclenche une erreur sur les expressions 'this' avec un type 'any' implicite.", "Re_exporting_a_type_when_the_isolatedModules_flag_is_provided_requires_using_export_type_1205": "La réexportation d'un type quand l'indicateur '--isolatedModules' est spécifié nécessite l'utilisation de 'export type'.", "Redirect_output_structure_to_the_directory_6006": "Rediriger la structure de sortie vers le répertoire.", "Reduce_the_number_of_projects_loaded_automatically_by_TypeScript_6617": "R<PERSON><PERSON><PERSON>z le nombre de projets chargés automatiquement par TypeScript.", "Referenced_project_0_may_not_disable_emit_6310": "Le projet référencé '{0}' ne doit pas désactiver l'émission.", "Referenced_project_0_must_have_setting_composite_Colon_true_6306": "Le projet référencé '{0}' doit avoir le paramètre \"composite\" avec la valeur true.", "Referenced_via_0_from_file_1_1400": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(e) via '{0}' à partir du fichier '{1}'", "Relative_import_paths_need_explicit_file_extensions_in_EcmaScript_imports_when_moduleResolution_is_n_2834": "Les chemins d’importation relatifs nécessitent des extensions de fichier explicites dans les importations EcmaScript quand '--moduleResolution' est 'node16' ou 'nodenext'. Envisagez d’ajouter une extension au chemin d’importation.", "Relative_import_paths_need_explicit_file_extensions_in_EcmaScript_imports_when_moduleResolution_is_n_2835": "Les chemins d’importation relatifs ont besoin d’extensions de fichier explicites dans les importations EcmaScript quand '--moduleResolution' est 'node16' ou 'nodenext'. Voulez-vous dire «{0}» ?", "Remove_a_list_of_directories_from_the_watch_process_6628": "Supprimez une liste de répertoires du processus d’observation.", "Remove_a_list_of_files_from_the_watch_mode_s_processing_6629": "Supprimez une liste de fichiers du traitement du mode espion.", "Remove_all_unnecessary_override_modifiers_95163": "Supprimer tous les modificateurs 'override' inutiles", "Remove_all_unnecessary_uses_of_await_95087": "Supprimer toutes les utilisations non nécessaires de 'await'", "Remove_all_unreachable_code_95051": "Supprimer tout le code inaccessible", "Remove_all_unused_labels_95054": "Supprimer toutes les étiquettes inutilisées", "Remove_braces_from_all_arrow_function_bodies_with_relevant_issues_95115": "Supprimer les accolades de tous les corps de fonction arrow présentant des problèmes pertinents", "Remove_braces_from_arrow_function_95060": "Supprimer les accolades de la fonction arrow", "Remove_braces_from_arrow_function_body_95112": "Supprimer les accolades du corps de fonction arrow", "Remove_import_from_0_90005": "Supprimer l'importation de '{0}'", "Remove_override_modifier_95161": "Supprimer un modificateur 'override'", "Remove_parentheses_95126": "Supprimer les parenthèses", "Remove_template_tag_90011": "Supprimer la balise template", "Remove_the_20mb_cap_on_total_source_code_size_for_JavaScript_files_in_the_TypeScript_language_server_6618": "Supprimez la limite de 20 Mo sur la taille totale du code source pour les fichiers JavaScript dans le serveur de langage TypeScript.", "Remove_type_from_import_declaration_from_0_90055": "Supprimer 'type' de la déclaration d’importation de « {0} »", "Remove_type_from_import_of_0_from_1_90056": "Supprimer 'type' de l’importation de « {0} » de «{1}»", "Remove_type_parameters_90012": "Supprimer les paramètres de type", "Remove_unnecessary_await_95086": "Supprimer toute utilisation non nécessaire de 'await'", "Remove_unreachable_code_95050": "Supprimer le code inaccessible", "Remove_unused_declaration_for_Colon_0_90004": "Supprimer la déclaration inutilisée pour : '{0}'", "Remove_unused_declarations_for_Colon_0_90041": "Supprimer les déclarations inutilisées pour '{0}'", "Remove_unused_destructuring_declaration_90039": "Supprimer la déclaration de déstructuration inutilisée", "Remove_unused_label_95053": "Supprimer l'étiquette inutilisée", "Remove_variable_statement_90010": "Supprimer l'instruction de variable", "Rename_param_tag_name_0_to_1_95173": "Renommez le nom de balise '@param' '{0}' en '{1}'", "Replace_0_with_Promise_1_90036": "Remplacer '{0}' par 'Promise<{1}>'", "Replace_all_unused_infer_with_unknown_90031": "Remplacer tous les 'infer' inutilisés par 'unknown'", "Replace_import_with_0_95015": "Remplacez l'importation par '{0}'.", "Replace_infer_0_with_unknown_90030": "Remplacer 'infer {0}' par 'unknown'", "Report_error_when_not_all_code_paths_in_function_return_a_value_6075": "Signalez une erreur quand les chemins du code de la fonction ne retournent pas tous une valeur.", "Report_errors_for_fallthrough_cases_in_switch_statement_6076": "Signalez les erreurs pour les case avec fallthrough dans une instruction switch.", "Report_errors_in_js_files_8019": "Signalez les erreurs dans les fichiers .js.", "Report_errors_on_unused_locals_6134": "Signaler les erreurs sur les variables locales inutilisées.", "Report_errors_on_unused_parameters_6135": "Signaler les erreurs sur les paramètres inutilisés.", "Require_undeclared_properties_from_index_signatures_to_use_element_accesses_6717": "Les propriétés non déclarées sont imposées à partir des signatures d'index pour permettre l'utilisation des accès aux éléments.", "Required_type_parameters_may_not_follow_optional_type_parameters_2706": "Les paramètres de type obligatoires ne peuvent pas être placés à la suite des paramètres de type optionnels.", "Resolution_for_module_0_was_found_in_cache_from_location_1_6147": "La résolution du module '{0}' a été trouvée dans le cache à l'emplacement '{1}'.", "Resolution_for_type_reference_directive_0_was_found_in_cache_from_location_1_6241": "La résolution de la directive de référence de type '{0}' a été trouvée dans le cache à l'emplacement '{1}'.", "Resolve_keyof_to_string_valued_property_names_only_no_numbers_or_symbols_6195": "<PERSON><PERSON><PERSON><PERSON> 'keyof' en noms de propriétés de valeur chaîne uniquement (aucun nombre ou symbole).", "Resolving_in_0_mode_with_conditions_1_6402": "Résolution en mode {0} avec des conditions {1}.", "Resolving_module_0_from_1_6086": "======== Résolution du module '{0}' à partir de '{1}'. ========", "Resolving_module_name_0_relative_to_base_url_1_2_6094": "Résolution du nom de module '{0}' par rapport à l'URL de base '{1}' - '{2}'.", "Resolving_real_path_for_0_result_1_6130": "Résolution du chemin réel pour '{0}', résultat '{1}'.", "Resolving_type_reference_directive_0_containing_file_1_6242": "======== Résolution de la directive de référence de type '{0}', fichier conteneur '{1}'. ========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_2_6116": "======== Résolution de la directive de référence de type '{0}', fichier conteneur '{1}', répertoire racine '{2}'. ========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_not_set_6123": "======== Résolution de la directive de référence de type '{0}', fichier conteneur '{1}', répertoire racine non défini. ========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_1_6127": "======== Résolution de la directive de référence de type '{0}', fichier conteneur non défini, répertoire racine '{1}'. ========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_not_set_6128": "======== Résolution de la directive de référence de type '{0}', fichier conteneur non défini, répertoire racine non défini. ========", "Resolving_with_primary_search_path_0_6121": "Résolution à l'aide du chemin de recherche primaire '{0}'.", "Rest_parameter_0_implicitly_has_an_any_type_7019": "Le paramètre rest '{0}' possède implicitement un type 'any[]'.", "Rest_parameter_0_implicitly_has_an_any_type_but_a_better_type_may_be_inferred_from_usage_7047": "Le paramètre Rest '{0}' a implicitement un type 'any[]', mais il est possible de déduire un meilleur type à partir de l'utilisation.", "Rest_types_may_only_be_created_from_object_types_2700": "Vous ne pouvez créer des types Rest qu'à partir de types d'objet.", "Return_type_annotation_circularly_references_itself_2577": "L'annotation de type de retour se référence de manière circulaire.", "Return_type_must_be_inferred_from_a_function_95149": "Le type de retour doit être déduit d'une fonction", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4046": "Le type de retour de la signature d'appel de l'interface exportée possède ou utilise le nom '{0}' du module privé '{1}'.", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_private_name_0_4047": "Le type de retour de la signature d'appel de l'interface exportée possède ou utilise le nom privé '{0}'.", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_name_0_from_private_mod_4044": "Le type de retour de la signature de constructeur de l'interface exportée possède ou utilise le nom '{0}' du module privé '{1}'.", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_0_4045": "Le type de retour de la signature de constructeur de l'interface exportée possède ou utilise le nom privé '{0}'.", "Return_type_of_constructor_signature_must_be_assignable_to_the_instance_type_of_the_class_2409": "Le type de retour de la signature de constructeur doit pouvoir être assigné au type d'instance de la classe.", "Return_type_of_exported_function_has_or_is_using_name_0_from_external_module_1_but_cannot_be_named_4058": "Le type de retour de la fonction exportée possède ou utilise le nom '{0}' du module externe {1}, mais il ne peut pas être nommé.", "Return_type_of_exported_function_has_or_is_using_name_0_from_private_module_1_4059": "Le type de retour de la fonction exportée possède ou utilise le nom '{0}' du module privé '{1}'.", "Return_type_of_exported_function_has_or_is_using_private_name_0_4060": "Le type de retour de la fonction exportée possède ou utilise le nom privé '{0}'.", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4048": "Le type de retour de la signature d'index de l'interface exportée possède ou utilise le nom '{0}' du module privé '{1}'.", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_private_name_0_4049": "Le type de retour de la signature d'index de l'interface exportée possède ou utilise le nom privé '{0}'.", "Return_type_of_method_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4056": "Le type de retour de la méthode de l'interface exportée possède ou utilise le nom '{0}' du module privé '{1}'.", "Return_type_of_method_from_exported_interface_has_or_is_using_private_name_0_4057": "Le type de retour de la méthode de l'interface exportée possède ou utilise le nom privé '{0}'.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_4041": "Le type de retour du getter public '{0}' de la classe exportée porte ou utilise le nom '{1}' du module externe {2}, mais il ne peut pas être nommé.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4042": "Le type de retour du getter public '{0}' de la classe exportée porte ou utilise le nom '{1}' du module privé '{2}'.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_private_name_1_4043": "Le type de retour du getter public '{0}' de la classe exportée porte ou utilise le nom privé '{1}'.", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_external_module_1_but_c_4053": "Le type de retour de la méthode publique de la classe exportée possède ou utilise le nom '{0}' du module externe {1}, mais il ne peut pas être nommé.", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4054": "Le type de retour de la méthode publique de la classe exportée possède ou utilise le nom '{0}' du module privé '{1}'.", "Return_type_of_public_method_from_exported_class_has_or_is_using_private_name_0_4055": "Le type de retour de la méthode publique de la classe exportée possède ou utilise le nom privé '{0}'.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_external_modul_4038": "Le type de retour du getter public '{0}' de la classe exportée porte ou utilise le nom '{1}' du module externe {2}, mais il ne peut pas être nommé.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_4039": "Le type de retour du getter public '{0}' de la classe exportée porte ou utilise le nom '{1}' du module privé '{2}'.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_private_name_1_4040": "Le type de retour du getter public '{0}' de la classe exportée porte ou utilise le nom privé '{1}'.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_external_module__4050": "Le type de retour de la méthode statique publique de la classe exportée possède ou utilise le nom '{0}' du module externe {1}, mais il ne peut pas être nommé.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4051": "Le type de retour de la méthode statique publique de la classe exportée possède ou utilise le nom '{0}' du module privé '{1}'.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_private_name_0_4052": "Le type de retour de la méthode statique publique de la classe exportée possède ou utilise le nom privé '{0}'.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_not_resolved_6395": "En réutilisant la résolution du module « {0} » à partir de « {1} » trouvée dans le cache de l’emplacement « {2} », l’erreur n’a pas été résolue.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6393": "En réutilisant la résolution du module « {0} » à partir de « {1} » trouvée dans le cache de l’emplacement « {2} », l’erreur a été résolue dans « {3} ».", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6394": "En réutilisant la résolution du module « {0} » à partir de « {1} » trouvée dans le cache de l’emplacement « {2} », l’erreur a été résolue dans « {3} » avec l’ID de package « {4} ».", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_not_resolved_6389": "En réutilisant la résolution du module « {0} » à partir de « {1} » de l’ancien programme, l’erreur n’a pas été résolue.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_6183": "En réutilisant la résolution du module « {0} » à partir de « {1} » de l’ancien programme, l’erreur a été résolue dans « {2} ».", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_with_Package__6184": "En réutilisant la résolution du module « {0} » à partir de « {1} » de l’ancien programme, l’erreur a été résolue dans « {2} avec l’ID de package « {3} ».", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_not_re_6398": "En réutilisant la résolution de la directive de référence de type « {0} » à partir de « {1} » trouvée dans le cache de l’emplacement « {2} », l’erreur n’a pas été résolue.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6396": "En réutilisant la résolution de la directive de référence de type « {0} » à partir de « {1} » trouvée dans le cache de l’emplacement « {2} », l’erreur a été résolue dans « {3} ».", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6397": "En réutilisant la résolution de la directive de référence de type « {0} » à partir de « {1} » trouvée dans le cache de l’emplacement « {2} », l’erreur a été résolue dans « {3} » avec l’ID de package « {4} ».", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_not_resolved_6392": "En réutilisant la résolution de la directive de référence de type « {0} » à partir de « {1} » de l’ancien programme, l’erreur n’a pas été résolue.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6390": "En réutilisant la résolution de la directive de référence de type « {0} » à partir de « {1} » de l’ancien programme, l’erreur a été résolue dans « {2} ».", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6391": "En réutilisant la résolution de la directive de référence de type « {0} » à partir de « {1} » de l’ancien programme, l’erreur a été résolue dans « {2} » avec l’ID de package « {3} ».", "Rewrite_all_as_indexed_access_types_95034": "Réécrire tout comme types d'accès indexés", "Rewrite_as_the_indexed_access_type_0_90026": "Réécrire en tant que type d'accès indexé '{0}'", "Root_directory_cannot_be_determined_skipping_primary_search_paths_6122": "Impossible de déterminer le répertoire racine, chemins de recherche primaires ignorés.", "Root_file_specified_for_compilation_1427": "Fichier racine spécifié pour la compilation", "STRATEGY_6039": "STRATÉGIE", "Save_tsbuildinfo_files_to_allow_for_incremental_compilation_of_projects_6642": "Enregistrez les fichiers .tsbuildinfo pour permettre la compilation incrémentielle des projets.", "Saw_non_matching_condition_0_6405": "Condition non correspondante '{0}' visible.", "Scoped_package_detected_looking_in_0_6182": "Package de portée détecté. Recherche dans '{0}'", "Selection_is_not_a_valid_statement_or_statements_95155": "La sélection ne correspond pas à une ou des instructions valides", "Selection_is_not_a_valid_type_node_95133": "La sélection n'est pas un nœud de type valide", "Set_the_JavaScript_language_version_for_emitted_JavaScript_and_include_compatible_library_declaratio_6705": "Définissez la version du langage JavaScript pour JavaScript émis et incluez des déclarations de bibliothèque compatibles.", "Set_the_language_of_the_messaging_from_TypeScript_This_does_not_affect_emit_6654": "Définissez la langue de la messagerie à partir de TypeScript. Cela n’affecte pas l’émission.", "Set_the_module_option_in_your_configuration_file_to_0_95099": "Affecter à l'option 'module' de votre fichier config la valeur '{0}'", "Set_the_newline_character_for_emitting_files_6659": "Définissez le caractère de nouvelle ligne pour l’émission de fichiers.", "Set_the_target_option_in_your_configuration_file_to_0_95098": "Affecter à l'option 'target' de votre fichier config la valeur '{0}'", "Setters_cannot_return_a_value_2408": "Les méthodes setter ne peuvent pas retourner de valeur.", "Show_all_compiler_options_6169": "Affichez toutes les options du compilateur.", "Show_diagnostic_information_6149": "Affichez les informations de diagnostic.", "Show_verbose_diagnostic_information_6150": "Affichez les informations de diagnostic détaillées.", "Show_what_would_be_built_or_deleted_if_specified_with_clean_6367": "<PERSON><PERSON> ce qui serait g<PERSON> (ou supprimé si '--clean' est spécifié)", "Signature_0_must_be_a_type_predicate_1224": "La signature '{0}' doit être un prédicat de type.", "Skip_type_checking_all_d_ts_files_6693": "Ignorer la vérification de type dans tous les fichiers .d.ts.", "Skip_type_checking_d_ts_files_that_are_included_with_TypeScript_6692": "Ignorer la vérification de type des fichiers .d.ts inclus dans TypeScript.", "Skip_type_checking_of_declaration_files_6012": "Ignorer le contrôle de type des fichiers de déclaration.", "Skipping_build_of_project_0_because_its_dependency_1_has_errors_6362": "Ignorer la génération du projet '{0}' car sa dépendance '{1}' comporte des erreurs", "Skipping_build_of_project_0_because_its_dependency_1_was_not_built_6382": "Ignorer la build du projet '{0}', car sa dépendance '{1}' n'a pas été générée", "Source_from_referenced_project_0_included_because_1_specified_1414": "Source du projet référencé '{0}' incluse, car '{1}' est spécifié", "Source_from_referenced_project_0_included_because_module_is_specified_as_none_1415": "Source du projet référencé '{0}' incluse, car '--module' est spécifié en tant que 'none'", "Source_has_0_element_s_but_target_allows_only_1_2619": "La source a {0} élément(s) mais la cible n'en autorise que {1}.", "Source_has_0_element_s_but_target_requires_1_2618": "La source a {0} élément(s) mais la cible en nécessite {1}.", "Source_provides_no_match_for_required_element_at_position_0_in_target_2623": "La source ne fournit aucune correspondance pour l'élément obligatoire situé à la position {0} dans la cible.", "Source_provides_no_match_for_variadic_element_at_position_0_in_target_2624": "La source ne fournit aucune correspondance pour l'élément variadique situé à la position {0} dans la cible.", "Specify_ECMAScript_target_version_6015": "Spécifiez la version cible ECMAScript.", "Specify_JSX_code_generation_6080": "Spécifiez la génération de code JSX.", "Specify_a_file_that_bundles_all_outputs_into_one_JavaScript_file_If_declaration_is_true_also_designa_6679": "Spécifiez un fichier qui regroupe toutes les sorties dans un fichier JavaScript. Si « declaration » a la valeur true, désigne également un fichier qui regroupe toutes les sorties .d.ts.", "Specify_a_list_of_glob_patterns_that_match_files_to_be_included_in_compilation_6641": "Spécifiez une liste de modèles glob qui correspondent aux fichiers à inclure dans la compilation.", "Specify_a_list_of_language_service_plugins_to_include_6681": "Spécifiez une liste de plug-ins de service de langage à ajouter.", "Specify_a_set_of_bundled_library_declaration_files_that_describe_the_target_runtime_environment_6651": "Spécifiez un ensemble de fichiers de déclaration de bibliothèque groupée qui décrivent l’environnement d’exécution cible.", "Specify_a_set_of_entries_that_re_map_imports_to_additional_lookup_locations_6680": "Spécifiez un ensemble d’entrées qui re-mappent les importations à d’autres emplacements de recherche.", "Specify_an_array_of_objects_that_specify_paths_for_projects_Used_in_project_references_6687": "Spécifiez un tableau d’objets qui spécifient les chemins d’accès pour les projets. Utilisé dans les références de projet.", "Specify_an_output_folder_for_all_emitted_files_6678": "Spécifiez un dossier de sortie pour tous les fichiers émis.", "Specify_emit_Slashchecking_behavior_for_imports_that_are_only_used_for_types_6718": "Spécifier le comportement d'émission/de vérification des importations utilisées uniquement pour les types.", "Specify_file_to_store_incremental_compilation_information_6380": "Spécifier le fichier de stockage des informations de compilation incrémentielle", "Specify_how_TypeScript_looks_up_a_file_from_a_given_module_specifier_6658": "Spécifiez comment TypeScript recherche un fichier à partir d’un spécificateur de module donné.", "Specify_how_directories_are_watched_on_systems_that_lack_recursive_file_watching_functionality_6714": "Spécifiez la façon dont les répertoires sont surveillés sur les systèmes qui ne disposent pas de fonctionnalités récursives de surveillance des fichiers.", "Specify_how_the_TypeScript_watch_mode_works_6715": "Spécifiez le fonctionnement du mode espion TypeScript.", "Specify_library_files_to_be_included_in_the_compilation_6079": "Spécifiez les fichiers bibliothèques à inclure dans la compilation.", "Specify_module_code_generation_6016": "Spécifier la génération de code de module.", "Specify_module_resolution_strategy_Colon_node_Node_js_or_classic_TypeScript_pre_1_6_6069": "Spécifiez la stratégie de résolution de module : 'node' (Node.js) ou 'classic' (version de TypeScript antérieure à 1.6).", "Specify_module_specifier_used_to_import_the_JSX_factory_functions_when_using_jsx_Colon_react_jsx_Ast_6649": "Spécifiez le spécificateur de module utilisé pour importer les fonctions de fabrique JSX lors de l’utilisation de « jsx: react-jsx* ».", "Specify_multiple_folders_that_act_like_Slashnode_modules_Slash_types_6710": "Spécifiez plusieurs dossiers qui agissent comme « ./node_modules/@types ».", "Specify_one_or_more_path_or_node_module_references_to_base_configuration_files_from_which_settings_a_6633": "Spécifiez une ou plusieurs références de module de chemin d’accès ou de nœud aux fichiers de configuration de base dont les paramètres sont hérités.", "Specify_options_for_automatic_acquisition_of_declaration_files_6709": "Spécifiez les options d’acquisition automatique des fichiers de déclaration.", "Specify_strategy_for_creating_a_polling_watch_when_it_fails_to_create_using_file_system_events_Colon_6227": "Spécifiez la stratégie en cas d'échec de la création d'une surveillance de l'interrogation à l'aide des événements liés au système de fichiers : 'FixedInterval' (par défaut), 'PriorityInterval', 'DynamicPriority', 'FixedChunkSize'.", "Specify_strategy_for_watching_directory_on_platforms_that_don_t_support_recursive_watching_natively__6226": "Spécifiez la stratégie de surveillance des répertoires sur les plateformes qui ne prennent pas en charge la surveillance récursive en mode natif : 'UseFsEvents' (par défaut), 'FixedPollingInterval', 'DynamicPriorityPolling', 'FixedChunkSizePolling'.", "Specify_strategy_for_watching_file_Colon_FixedPollingInterval_default_PriorityPollingInterval_Dynami_6225": "Spécifiez la stratégie de surveillance des fichiers : 'FixedPollingInterval' (par défaut), 'PriorityPollingInterval', 'DynamicPriorityPolling', 'FixedChunkSizePolling', 'UseFsEvents', 'UseFsEventsOnParentDirectory'.", "Specify_the_JSX_Fragment_reference_used_for_fragments_when_targeting_React_JSX_emit_e_g_React_Fragme_6648": "Spécifiez la référence de fragment JSX utilisée pour les fragments lors du ciblage de l’émission de réaction JSX par exemple « React.Fragment » ou « Fragment ».", "Specify_the_JSX_factory_function_to_use_when_targeting_react_JSX_emit_e_g_React_createElement_or_h_6146": "Spécifiez la fonction de fabrique JSX à utiliser pour le ciblage d'une émission JSX 'react', par exemple 'React.createElement' ou 'h'.", "Specify_the_JSX_factory_function_used_when_targeting_React_JSX_emit_e_g_React_createElement_or_h_6647": "Spécifiez la fonction de fabrique JSX à utiliser pour le ciblage d'une émission JSX « react », par exemple « React.createElement » ou « h ».", "Specify_the_JSX_fragment_factory_function_to_use_when_targeting_react_JSX_emit_with_jsxFactory_compi_18034": "Spécifiez la fonction de fabrique de fragments JSX à utiliser durant le ciblage de l'émission JSX 'react' avec l'option de compilateur 'jsxFactory', par exemple 'Fragment'.", "Specify_the_base_directory_to_resolve_non_relative_module_names_6607": "Spécifiez le répertoire de base pour résoudre les noms de modules non relatifs.", "Specify_the_end_of_line_sequence_to_be_used_when_emitting_files_Colon_CRLF_dos_or_LF_unix_6060": "Spécifiez la séquence de fin de ligne à utiliser durant l'émission des fichiers : 'CRLF' (Dos) ou 'LF' (Unix).", "Specify_the_location_where_debugger_should_locate_TypeScript_files_instead_of_source_locations_6004": "Spécifiez l'emplacement dans lequel le débogueur doit localiser les fichiers TypeScript au lieu des emplacements sources.", "Specify_the_location_where_debugger_should_locate_map_files_instead_of_generated_locations_6655": "Spécifiez l'emplacement dans lequel le débogueur doit localiser les fichiers de mappage au lieu des emplacements générés.", "Specify_the_maximum_folder_depth_used_for_checking_JavaScript_files_from_node_modules_Only_applicabl_6656": "Spécifiez la profondeur maximale de dossier utilisée pour la vérification des fichiers JavaScript à partir de « node_modules ». Applicable uniquement à l’aide de « allowJs ».", "Specify_the_module_specifier_to_be_used_to_import_the_jsx_and_jsxs_factory_functions_from_eg_react_6238": "Spécifiez le spécificateur de module à utiliser pour importer les fonctions de fabrique 'jsx' et 'jsxs' à partir de react, par exemple", "Specify_the_object_invoked_for_createElement_This_only_applies_when_targeting_react_JSX_emit_6686": "Spécifiez l’objet appelé pour « createElement ». Ceci s’applique uniquement quand le ciblage de l’émission de JSX « react » est actif.", "Specify_the_output_directory_for_generated_declaration_files_6613": "Spécifiez le répertoire de sortie pour les fichiers de déclaration générés.", "Specify_the_path_to_tsbuildinfo_incremental_compilation_file_6707": "Spécifiez le chemin d’accès au fichier de compilation incrémentielle .incrémentielle .", "Specify_the_root_directory_of_input_files_Use_to_control_the_output_directory_structure_with_outDir_6058": "Spécifiez le répertoire racine des fichiers d'entrée. Contrôlez la structure des répertoires de sortie avec --outDir.", "Specify_the_root_folder_within_your_source_files_6690": "Spécifiez le dossier racine dans vos fichiers sources.", "Specify_the_root_path_for_debuggers_to_find_the_reference_source_code_6695": "Spécifiez le chemin d’accès racine des débogueurs pour trouver le code source de référence.", "Specify_type_package_names_to_be_included_without_being_referenced_in_a_source_file_6711": "Spécifiez les noms de packages de types à inclure sans être référencés dans un fichier source.", "Specify_what_JSX_code_is_generated_6646": "Spécifiez le code JSX généré.", "Specify_what_approach_the_watcher_should_use_if_the_system_runs_out_of_native_file_watchers_6634": "Spécifiez l’approche que l’observateur doit utiliser si le système manque d’observateurs de fichiers natifs.", "Specify_what_module_code_is_generated_6657": "Spécifiez le code de module généré.", "Split_all_invalid_type_only_imports_1367": "Diviser toutes les importations de type uniquement non valides", "Split_into_two_separate_import_declarations_1366": "Diviser en deux déclarations import distinctes", "Spread_operator_in_new_expressions_is_only_available_when_targeting_ECMAScript_5_and_higher_2472": "L'opérateur spread dans les expressions 'new' est disponible uniquement quand ECMAScript 5 ou version supérieure est ciblé.", "Spread_types_may_only_be_created_from_object_types_2698": "Vous ne pouvez créer des types Spread qu'à partir de types d'objet.", "Starting_compilation_in_watch_mode_6031": "Démarrage de la compilation en mode espion...", "Statement_expected_1129": "Instruction attendue.", "Statements_are_not_allowed_in_ambient_contexts_1036": "Les instructions ne sont pas autorisées dans les contextes ambiants.", "Static_members_cannot_reference_class_type_parameters_2302": "Les membres statiques ne peuvent pas référencer des paramètres de type de classe.", "Static_property_0_conflicts_with_built_in_property_Function_0_of_constructor_function_1_2699": "La propriété statique '{0}' est en conflit avec la propriété intégrée 'Function.{0}' de la fonction constructeur '{1}'.", "String_literal_expected_1141": "Littéral de chaîne attend<PERSON>.", "String_literal_with_double_quotes_expected_1327": "Littéral de chaîne avec guillemets doubles attendu.", "Stylize_errors_and_messages_using_color_and_context_experimental_6073": "<PERSON><PERSON><PERSON><PERSON> les erreurs et les messages avec de la couleur et du contexte (expérimental).", "Subsequent_property_declarations_must_have_the_same_type_Property_0_must_be_of_type_1_but_here_has_t_2717": "Les prochaines déclarations de propriétés doivent avoir le même type. La propriété '{0}' doit avoir le type '{1}', mais elle a ici le type '{2}'.", "Subsequent_variable_declarations_must_have_the_same_type_Variable_0_must_be_of_type_1_but_here_has_t_2403": "Les déclarations de variable ultérieures doivent avoir le même type. La variable '{0}' doit être de type '{1}', mais elle a ici le type '{2}'.", "Substitution_0_for_pattern_1_has_incorrect_type_expected_string_got_2_5064": "Le type de la substitution '{0}' du modèle '{1}' est incorrect. Attente de 'string'. Obtention de '{2}'.", "Substitution_0_in_pattern_1_can_have_at_most_one_Asterisk_character_5062": "La substitution '{0}' dans le modèle '{1}' ne peut avoir qu'un seul caractère '*' au maximum.", "Substitutions_for_pattern_0_should_be_an_array_5063": "Les substitutions du modèle '{0}' doivent correspondre à un tableau.", "Substitutions_for_pattern_0_shouldn_t_be_an_empty_array_5066": "Les substitutions du modèle '{0}' ne doivent pas correspondre à un tableau vide.", "Successfully_created_a_tsconfig_json_file_6071": "Un fichier tsconfig.json a été créé.", "Super_calls_are_not_permitted_outside_constructors_or_in_nested_functions_inside_constructors_2337": "Les appels de 'super' ne sont pas autorisés hors des constructeurs ou dans des fonctions imbriquées dans des constructeurs.", "Suppress_excess_property_checks_for_object_literals_6072": "Supprimez les vérifications des propriétés en trop pour les littéraux d'objet.", "Suppress_noImplicitAny_errors_for_indexing_objects_lacking_index_signatures_6055": "Supprimer les erreurs noImplicitAny pour les objets d'indexation auxquels il manque des signatures d'index.", "Suppress_noImplicitAny_errors_when_indexing_objects_that_lack_index_signatures_6703": "Supprimez les erreurs « noImplicitAny » lors de l’indexation d’objets qui n’ont pas de signatures d’index.", "Switch_each_misused_0_to_1_95138": "Remplacer chaque utilisation incorrecte de '{0}' par '{1}'", "Synchronously_call_callbacks_and_update_the_state_of_directory_watchers_on_platforms_that_don_t_supp_6704": "Appelez les rappels de façon synchrone, et mettez à jour l'état des observateurs de répertoire sur les plateformes qui ne prennent pas en charge la surveillance récursive en mode natif.", "Syntax_Colon_0_6023": "Syntaxe : {0}", "Tag_0_expects_at_least_1_arguments_but_the_JSX_factory_2_provides_at_most_3_6229": "La balise '{0}' attend au moins '{1}' arguments, mais la fabrique JSX '{2}' en fournit au maximum '{3}'.", "Tagged_template_expressions_are_not_permitted_in_an_optional_chain_1358": "Les expressions de modèle étiquetées ne sont pas autorisées dans une chaîne facultative.", "Target_allows_only_0_element_s_but_source_may_have_more_2621": "La cible autorise uniquement {0} élément(s) mais la source peut en avoir plus.", "Target_requires_0_element_s_but_source_may_have_fewer_2620": "La cible nécessite {0} élément(s) mais la source peut en avoir moins.", "The_0_modifier_can_only_be_used_in_TypeScript_files_8009": "Le modificateur '{0}' peut uniquement être utilisé dans les fichiers TypeScript.", "The_0_operator_cannot_be_applied_to_type_symbol_2469": "Impossible d'appliquer l'opérateur '{0}' au type 'symbol'.", "The_0_operator_is_not_allowed_for_boolean_types_Consider_using_1_instead_2447": "L'opérateur '{0}' n'est pas autorisé pour les types booléens. Utilisez '{1}' à la place.", "The_0_property_of_an_async_iterator_must_be_a_method_2768": "La propriété '{0}' d'un itérateur asynchrone doit être une méthode.", "The_0_property_of_an_iterator_must_be_a_method_2767": "La propriété '{0}' d'un itérateur doit être une méthode.", "The_Object_type_is_assignable_to_very_few_other_types_Did_you_mean_to_use_the_any_type_instead_2696": "Le type 'Object' peut être assigné à très peu d'autres types. Souhaitez-vous utiliser le type 'any' à la place ?", "The_arguments_object_cannot_be_referenced_in_an_arrow_function_in_ES3_and_ES5_Consider_using_a_stand_2496": "Impossible de référencer l'objet 'arguments' dans une fonction arrow dans ES3 et ES5. Utilisez plutôt une expression de fonction standard.", "The_arguments_object_cannot_be_referenced_in_an_async_function_or_method_in_ES3_and_ES5_Consider_usi_2522": "Les objets 'arguments' ne peuvent pas être référencés dans une fonction ou méthode async en ES3 et ES5. Utilisez une fonction ou méthode standard.", "The_body_of_an_if_statement_cannot_be_the_empty_statement_1313": "Le corps d'une instruction 'if' ne peut pas être l'instruction vide.", "The_call_would_have_succeeded_against_this_implementation_but_implementation_signatures_of_overloads_2793": "L'appel aurait pu réussir sur cette implémentation, mais les signatures de surcharges de l'implémentation ne sont pas visibles en externe.", "The_character_set_of_the_input_files_6163": "Jeu de caractères des fichiers d'entrée.", "The_containing_arrow_function_captures_the_global_value_of_this_7041": "La fonction arrow conteneur capture la valeur globale de 'this'.", "The_containing_function_or_module_body_is_too_large_for_control_flow_analysis_2563": "Le corps de la fonction ou du module conteneur est trop grand pour l'analyse du flux de contrôle.", "The_current_file_is_a_CommonJS_module_and_cannot_use_await_at_the_top_level_1309": "Le fichier actuel est un module CommonJS et ne peut pas utiliser 'await' au niveau supérieur.", "The_current_file_is_a_CommonJS_module_whose_imports_will_produce_require_calls_however_the_reference_1479": "Le fichier actuel est un module CommonJS dont les importations produiront des appels 'require' ; cependant, le fichier référencé est un module ECMAScript et ne peut pas être importé avec 'require'. Envisagez d'écrire un appel dynamique 'import(\"{0}\")' à la place.", "The_current_host_does_not_support_the_0_option_5001": "L'hôte actuel ne prend pas en charge l'option '{0}'.", "The_declaration_of_0_that_you_probably_intended_to_use_is_defined_here_18018": "La déclaration de '{0}' que vous aviez probablement l'intention d'utiliser est définie ici", "The_declaration_was_marked_as_deprecated_here_2798": "La déclaration a été marquée ici comme étant dépréciée.", "The_expected_type_comes_from_property_0_which_is_declared_here_on_type_1_6500": "Le type attendu provient de la propriété '{0}', qui est déclarée ici sur le type '{1}'", "The_expected_type_comes_from_the_return_type_of_this_signature_6502": "Le type attendu provient du type de retour de cette signature.", "The_expected_type_comes_from_this_index_signature_6501": "Le type attendu provient de cette signature d'index.", "The_expression_of_an_export_assignment_must_be_an_identifier_or_qualified_name_in_an_ambient_context_2714": "L'expression d'une assignation d'exportation doit être un identificateur ou un nom qualifié dans un contexte ambiant.", "The_file_is_in_the_program_because_Colon_1430": "Le fichier est dans le programme pour la ou les raisons suivantes :", "The_files_list_in_config_file_0_is_empty_18002": "La liste 'files' du fichier config '{0}' est vide.", "The_first_export_default_is_here_2752": "La première valeur par défaut d'exportation se trouve ici.", "The_first_parameter_of_the_then_method_of_a_promise_must_be_a_callback_1060": "Le premier paramètre de la méthode 'then' d'une promesse doit être un rappel.", "The_global_type_JSX_0_may_not_have_more_than_one_property_2608": "Le type global 'JSX.{0}' ne peut pas avoir plusieurs propriétés.", "The_implementation_signature_is_declared_here_2750": "La signature d'implémentation est déclarée ici.", "The_import_meta_meta_property_is_not_allowed_in_files_which_will_build_into_CommonJS_output_1470": "La métapropriété « import.meta » n’est pas autorisée dans les fichiers qui seront intégrés dans la sortie CommonJS.", "The_import_meta_meta_property_is_only_allowed_when_the_module_option_is_es2020_es2022_esnext_system__1343": "La méta-propriété 'import.meta' est autorisée uniquement lorsque l’option '--module' est 'es2020', 'es2022', 'esnext', 'system', 'node16' ou 'nodenext'.", "The_inferred_type_of_0_cannot_be_named_without_a_reference_to_1_This_is_likely_not_portable_A_type_a_2742": "Le type déduit de '{0}' ne peut pas être nommé sans référence à '{1}'. Cela n'est probablement pas portable. Une annotation de type est nécessaire.", "The_inferred_type_of_0_references_a_type_with_a_cyclic_structure_which_cannot_be_trivially_serialize_5088": "Le type déduit de '{0}' référence un type avec une structure cyclique qui ne peut pas être sérialisée de manière triviale. Une annotation de type est nécessaire.", "The_inferred_type_of_0_references_an_inaccessible_1_type_A_type_annotation_is_necessary_2527": "Le type déduit de '{0}' référence un type '{1}' inaccessible. Une annotation de type est nécessaire.", "The_inferred_type_of_this_node_exceeds_the_maximum_length_the_compiler_will_serialize_An_explicit_ty_7056": "Le type déduit de ce nœud dépasse la longueur maximale que le compilateur va sérialiser. Une annotation de type explicite est nécessaire.", "The_intersection_0_was_reduced_to_never_because_property_1_exists_in_multiple_constituents_and_is_pr_18032": "L'intersection '{0}' a été réduite à 'never', car la propriété '{1}' existe dans plusieurs constituants et est privée dans certains d'entre eux.", "The_intersection_0_was_reduced_to_never_because_property_1_has_conflicting_types_in_some_constituent_18031": "L'intersection '{0}' a été réduite à 'never', car la propriété '{1}' a des types en conflit dans certains constituants.", "The_intrinsic_keyword_can_only_be_used_to_declare_compiler_provided_intrinsic_types_2795": "Le mot clé 'intrinsic' peut uniquement être utilisé pour déclarer les types intrinsèques fournis par le compilateur.", "The_jsxFragmentFactory_compiler_option_must_be_provided_to_use_JSX_fragments_with_the_jsxFactory_com_17016": "L'option de compilateur 'jsxFragmentFactory' doit être fournie pour permettre l'utilisation des fragments JSX avec l'option de compilateur 'jsxFactory'.", "The_last_overload_gave_the_following_error_2770": "La dernière surcharge a généré l'erreur suivante.", "The_last_overload_is_declared_here_2771": "La dernière surcharge est déclarée ici.", "The_left_hand_side_of_a_for_in_statement_cannot_be_a_destructuring_pattern_2491": "La partie gauche d'une instruction 'for...in' ne peut pas être un modèle de déstructuration.", "The_left_hand_side_of_a_for_in_statement_cannot_use_a_type_annotation_2404": "La partie gauche d'une instruction 'for...in' ne peut pas utiliser d'annotation de type.", "The_left_hand_side_of_a_for_in_statement_may_not_be_an_optional_property_access_2780": "La partie gauche d'une instruction 'for...in' ne doit pas être un accès à une propriété facultative.", "The_left_hand_side_of_a_for_in_statement_must_be_a_variable_or_a_property_access_2406": "La partie gauche d'une instruction 'for...in' doit être un accès à une variable ou une propriété.", "The_left_hand_side_of_a_for_in_statement_must_be_of_type_string_or_any_2405": "La partie gauche d'une instruction 'for...in' doit être de type 'string' ou 'any'.", "The_left_hand_side_of_a_for_of_statement_cannot_use_a_type_annotation_2483": "La partie gauche d'une instruction 'for...of' ne peut pas utiliser d'annotation de type.", "The_left_hand_side_of_a_for_of_statement_may_not_be_an_optional_property_access_2781": "La partie gauche d'une instruction 'for...of' ne doit pas être un accès à une propriété facultative.", "The_left_hand_side_of_a_for_of_statement_may_not_be_async_1106": "Il est possible que le côté gauche d’une instruction « for...of » ne soit pas « async ».", "The_left_hand_side_of_a_for_of_statement_must_be_a_variable_or_a_property_access_2487": "La partie gauche d'une instruction 'for...of' doit être un accès à une variable ou une propriété.", "The_left_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2362": "La partie gauche d'une opération arithmétique doit être de type 'any', 'number', 'bigint' ou un type enum.", "The_left_hand_side_of_an_assignment_expression_may_not_be_an_optional_property_access_2779": "La partie gauche d'une expression d'assignation ne doit pas être un accès à une propriété facultative.", "The_left_hand_side_of_an_assignment_expression_must_be_a_variable_or_a_property_access_2364": "La partie gauche d'une expression d'assignation doit être un accès à une variable ou une propriété.", "The_left_hand_side_of_an_instanceof_expression_must_be_of_type_any_an_object_type_or_a_type_paramete_2358": "La partie gauche d'une expression 'instanceof' doit être de type 'any', un type d'objet ou un paramètre de type.", "The_locale_used_when_displaying_messages_to_the_user_e_g_en_us_6156": "Paramètres régionaux utilisés pour afficher les messages à l'utilisateur (exemple : 'fr-fr')", "The_maximum_dependency_depth_to_search_under_node_modules_and_load_JavaScript_files_6136": "Profondeur de dépendance maximale pour la recherche sous node_modules et le chargement de fichiers JavaScript.", "The_operand_of_a_delete_operator_cannot_be_a_private_identifier_18011": "L'opérande d'un opérateur 'delete' ne peut pas être un identificateur privé.", "The_operand_of_a_delete_operator_cannot_be_a_read_only_property_2704": "L'opérande d'un opérateur 'delete' ne peut pas être une propriété en lecture seule.", "The_operand_of_a_delete_operator_must_be_a_property_reference_2703": "L'opérande d'un opérateur 'delete' doit être une référence de propriété.", "The_operand_of_a_delete_operator_must_be_optional_2790": "L'opérande d'un opérateur 'delete' doit être facultatif.", "The_operand_of_an_increment_or_decrement_operator_may_not_be_an_optional_property_access_2777": "L'opérande d'un opérateur d'incrémentation ou de décrémentation ne doit pas être un accès à une propriété facultative.", "The_operand_of_an_increment_or_decrement_operator_must_be_a_variable_or_a_property_access_2357": "L'opérande d'un opérateur d'incrémentation ou de décrémentation doit être un accès à une variable ou une propriété.", "The_parser_expected_to_find_a_1_to_match_the_0_token_here_1007": "L'analyseur s'attendait à trouver '{1}' pour correspondre au jeton '{0}' ici.", "The_project_root_is_ambiguous_but_is_required_to_resolve_export_map_entry_0_in_file_1_Supply_the_roo_2209": "La racine du projet est ambiguë, mais elle est nécessaire pour résoudre l’entrée de carte d’exportation '{0}' dans le fichier '{1}'. Fournissez l’option de compilateur « rootDir » pour lever l’ambiguïté.", "The_project_root_is_ambiguous_but_is_required_to_resolve_import_map_entry_0_in_file_1_Supply_the_roo_2210": "La racine du projet est ambiguë, mais elle est nécessaire pour résoudre l’entrée de carte d’importation «{0}» dans le fichier «{1}». Fournissez l’option de compilateur « rootDir » pour lever l’ambiguïté.", "The_property_0_cannot_be_accessed_on_type_1_within_this_class_because_it_is_shadowed_by_another_priv_18014": "La propriété '{0}' n'est pas accessible sur le type '{1}' dans cette classe, car elle est mise en mémoire fantôme par un autre identificateur privé ayant la même orthographe.", "The_return_type_of_a_get_accessor_must_be_assignable_to_its_set_accessor_type_2380": "Le type de retour d'un accesseur 'get' doit être assignable à son type d'accesseur 'set'", "The_return_type_of_a_parameter_decorator_function_must_be_either_void_or_any_1237": "Le type de retour d'une fonction d'élément décoratif de paramètre doit être 'void' ou 'any'.", "The_return_type_of_a_property_decorator_function_must_be_either_void_or_any_1236": "Le type de retour d'une fonction d'élément décoratif de propriété doit être 'void' ou 'any'.", "The_return_type_of_an_async_function_must_either_be_a_valid_promise_or_must_not_contain_a_callable_t_1058": "Le type de retour d'une fonction asynchrone doit être une promesse valide ou ne doit contenir aucun membre 'then' pouvant être appelé.", "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_Did_you_mean_to_wri_1064": "Le type de retour d'une fonction ou d'une méthode asynchrone doit être le type global Promise<T>. Vouliez-vous vraiment écrire 'Promise<{0}>' ?", "The_right_hand_side_of_a_for_in_statement_must_be_of_type_any_an_object_type_or_a_type_parameter_but_2407": "La partie droite d'une instruction 'for...in' doit être de type 'any', un type d'objet ou un paramètre de type, mais elle a le type '{0}' ici.", "The_right_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2363": "La partie droite d'une opération arithmétique doit être de type 'any', 'number', 'bigint' ou un type enum.", "The_right_hand_side_of_an_instanceof_expression_must_be_of_type_any_or_of_a_type_assignable_to_the_F_2359": "La partie droite d'une expression 'instanceof' doit être de type 'any' ou d'un type pouvant être assigné au type d'interface 'Function'.", "The_root_value_of_a_0_file_must_be_an_object_5092": "La valeur racine d'un fichier '{0}' doit être un objet.", "The_shadowing_declaration_of_0_is_defined_here_18017": "La déclaration avec mise en mémoire fantôme de '{0}' est définie ici", "The_signature_0_of_1_is_deprecated_6387": "La signature '{0}' de '{1}' est dépréciée.", "The_specified_path_does_not_exist_Colon_0_5058": "Le chemin spécifié n'existe pas : '{0}'.", "The_tag_was_first_specified_here_8034": "La balise a d'abord été spécifiée ici.", "The_target_of_an_object_rest_assignment_may_not_be_an_optional_property_access_2778": "La cible d'une assignation rest d'objet ne doit pas être un accès à une propriété facultative.", "The_target_of_an_object_rest_assignment_must_be_a_variable_or_a_property_access_2701": "La cible de l'assignation du reste d'un objet doit être un accès à une variable ou une propriété.", "The_this_context_of_type_0_is_not_assignable_to_method_s_this_of_type_1_2684": "Le contexte 'this' de type '{0}' n'est pas assignable au contexte 'this' de type '{1}' de la méthode.", "The_this_types_of_each_signature_are_incompatible_2685": "Les types 'this' de chaque signature sont incompatibles.", "The_type_0_is_readonly_and_cannot_be_assigned_to_the_mutable_type_1_4104": "Le type '{0}' est 'readonly' et ne peut pas être assigné au type modifiable '{1}'.", "The_type_modifier_cannot_be_used_on_a_named_export_when_export_type_is_used_on_its_export_statement_2207": "Le ’type’ de modificateur ne peut pas être utilisé sur une importation nommée quand le ’type’ d’exportation est utilisé dans son instruction import.", "The_type_modifier_cannot_be_used_on_a_named_import_when_import_type_is_used_on_its_import_statement_2206": "Le ’type’ de modificateur ne peut pas être utilisé sur une importation nommée quand le ’type’ d’importation est utilisé dans son instruction import.", "The_type_of_a_function_declaration_must_match_the_function_s_signature_8030": "Le type d'une déclaration de fonction doit correspondre à la signature de la fonction.", "The_type_of_this_expression_cannot_be_named_without_a_resolution_mode_assertion_which_is_an_unstable_2841": "Le type de cette expression ne peut pas être nommé sans une assertion de « mode résolution », qui est une fonctionnalité instable. Utilisez TypeScript nocturne pour désactiver cette erreur. Essayez de mettre à jour avec « npm install -D typescript@next ».", "The_type_of_this_node_cannot_be_serialized_because_its_property_0_cannot_be_serialized_4118": "Impossible de sérialiser le type de ce nœud, car sa propriété «{0}» ne peut pas être sérialisée.", "The_type_returned_by_the_0_method_of_an_async_iterator_must_be_a_promise_for_a_type_with_a_value_pro_2547": "Le type retourné par la méthode '{0}()' d'un itérateur asynchrone doit être une promesse pour un type ayant une propriété 'value'.", "The_type_returned_by_the_0_method_of_an_iterator_must_have_a_value_property_2490": "Le type retourné par la méthode '{0}()' d'un itérateur doit avoir une propriété 'value'.", "The_types_of_0_are_incompatible_between_these_types_2200": "Les types de '{0}' sont incompatibles entre eux.", "The_types_returned_by_0_are_incompatible_between_these_types_2201": "Les types retournés par '{0}' sont incompatibles entre eux.", "The_value_0_cannot_be_used_here_18050": "La valeur '{0}' ne peut pas être utilisée ici.", "The_variable_declaration_of_a_for_in_statement_cannot_have_an_initializer_1189": "La déclaration de variable d'une instruction 'for...in' ne peut pas avoir d'initialiseur.", "The_variable_declaration_of_a_for_of_statement_cannot_have_an_initializer_1190": "La déclaration de variable d'une instruction 'for...of' ne peut pas avoir d'initialiseur.", "The_with_statement_is_not_supported_All_symbols_in_a_with_block_will_have_type_any_2410": "L'instruction 'with' n'est pas prise en charge. Tous les symboles d'un bloc 'with' ont le type 'any'.", "This_JSX_tag_s_0_prop_expects_a_single_child_of_type_1_but_multiple_children_were_provided_2746": "La propriété '{0}' de cette balise JSX attend un seul enfant de type '{1}', mais plusieurs enfants ont été fournis.", "This_JSX_tag_s_0_prop_expects_type_1_which_requires_multiple_children_but_only_a_single_child_was_pr_2745": "La propriété '{0}' de cette balise JSX attend le type '{1}', qui nécessite plusieurs enfants, mais un seul enfant a été fourni.", "This_comparison_appears_to_be_unintentional_because_the_types_0_and_1_have_no_overlap_2367": "Cette comparaison semble involontaire, car les types '{0}' et '{1}' n’ont pas de chevauchement.", "This_condition_will_always_return_0_2845": "Cette condition retourne toujours '{0}'.", "This_condition_will_always_return_0_since_JavaScript_compares_objects_by_reference_not_value_2839": "Cette condition retourne toujours '{0}', car JavaScript compare les objets par référence, et non par valeur.", "This_condition_will_always_return_true_since_this_0_is_always_defined_2801": "Cette condition retourne toujours true, car cette « {0} » est toujours définie.", "This_condition_will_always_return_true_since_this_function_is_always_defined_Did_you_mean_to_call_it_2774": "Cette condition retourne toujours true, car cette fonction est toujours définie. Est-ce que vous avez voulu l'appeler à la place ?", "This_constructor_function_may_be_converted_to_a_class_declaration_80002": "Cette fonction constructeur peut être convertie en déclaration de classe.", "This_expression_is_not_callable_2349": "Impossible d'appeler cette expression.", "This_expression_is_not_callable_because_it_is_a_get_accessor_Did_you_mean_to_use_it_without_6234": "Impossible d'appeler cette expression, car il s'agit d'un accesseur 'get'. Voulez-vous vraiment l'utiliser sans '()' ?", "This_expression_is_not_constructable_2351": "Impossible de construire cette expression.", "This_file_already_has_a_default_export_95130": "Ce fichier a déjà une exportation par défaut", "This_import_is_never_used_as_a_value_and_must_use_import_type_because_importsNotUsedAsValues_is_set__1371": "Cette importation n'est jamais utilisée en tant que valeur. Elle doit utiliser 'import type', car 'importsNotUsedAsValues' a la valeur 'error'.", "This_is_the_declaration_being_augmented_Consider_moving_the_augmenting_declaration_into_the_same_fil_6233": "<PERSON>ci est la déclaration augmentée. Pensez à déplacer la déclaration d'augmentation dans le même fichier.", "This_may_be_converted_to_an_async_function_80006": "Ceci peut être converti en fonction asynchrone.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4122": "Ce membre ne peut pas avoir de commentaire JSDoc avec une balise '@override' car il n'est pas déclaré dans la classe de base '{0}'.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4123": "Ce membre ne peut pas avoir de commentaire JSDoc avec une balise 'override' car il n'est pas déclaré dans la classe de base '{0}'. V<PERSON><PERSON>z-vous dire '{1}' ?", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_its_containing_class_0_does_not_4121": "Ce membre ne peut pas avoir de commentaire JSDoc avec une balise '@override' car sa classe conteneur '{0}' n'étend pas une autre classe.", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_4113": "Ce membre ne peut pas avoir de modificateur 'override', car il n'est pas déclaré dans la classe de base '{0}'.", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_Did_you__4117": "Ce membre ne peut pas avoir de modificateur 'override', car il n'est pas déclaré dans la classe de base '{0}'. V<PERSON><PERSON>z-vous dire '{1}'?", "This_member_cannot_have_an_override_modifier_because_its_containing_class_0_does_not_extend_another__4112": "Ce membre ne peut pas avoir de modificateur 'override', car sa classe conteneur '{0}' n'étend pas une autre classe.", "This_member_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_in_the_base_4119": "Ce membre doit avoir un commentaire JSDoc avec une balise '@override' car il remplace un membre de la classe de base '{0}'.", "This_member_must_have_an_override_modifier_because_it_overrides_a_member_in_the_base_class_0_4114": "Ce membre doit avoir un modificateur 'override', car il se substitue à un membre de la classe de base '{0}'.", "This_member_must_have_an_override_modifier_because_it_overrides_an_abstract_method_that_is_declared__4116": "Ce membre doit avoir un modificateur 'override', car il se substitue à une méthode abstraite déclarée dans la classe de base '{0}'.", "This_module_can_only_be_referenced_with_ECMAScript_imports_Slashexports_by_turning_on_the_0_flag_and_2497": "Vous pouvez référencer ce module uniquement avec les importations/exportations ECMAScript en activant l'indicateur '{0}' et en référençant son exportation par défaut.", "This_module_is_declared_with_export_and_can_only_be_used_with_a_default_import_when_using_the_0_flag_2594": "Ce module est déclaré avec 'export =', et ne peut être utilisé qu’avec une importation par défaut lors de l’utilisation de l’indicateur '{0}'.", "This_overload_signature_is_not_compatible_with_its_implementation_signature_2394": "Cette signature de surcharge n'est pas compatible avec sa signature d'implémentation.", "This_parameter_is_not_allowed_with_use_strict_directive_1346": "Ce paramètre n'est pas autorisé avec la directive 'use strict'.", "This_parameter_property_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_4120": "Cette propriété de paramètre doit avoir un commentaire JSDoc avec une balise '@override' car elle remplace un membre dans la classe de base '{0}'.", "This_parameter_property_must_have_an_override_modifier_because_it_overrides_a_member_in_base_class_0_4115": "Cette propriété de paramètre doit avoir un modificateur 'override', car il se substitue à un membre de la classe de base '{0}'.", "This_spread_always_overwrites_this_property_2785": "Cette diffusion écrase toujours cette propriété.", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Add_a_trailing_comma_or_explicit_cons_7060": "Cette syntaxe est réservée dans les fichiers avec l’extension .mts ou .cts. Veuillez ajouter une virgule de fin ou une contrainte explicite.", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Use_an_as_expression_instead_7059": "Cette syntaxe est réservée dans les fichiers avec l’extension .mts ou .cts. Utilisez une expression « as »à la place.", "This_syntax_requires_an_imported_helper_but_module_0_cannot_be_found_2354": "Cette syntaxe nécessite une application d'assistance importée, mais le module '{0}' est introuvable.", "This_syntax_requires_an_imported_helper_named_1_which_does_not_exist_in_0_Consider_upgrading_your_ve_2343": "Cette syntaxe nécessite une assistance importée nommée '{1}' mais qui n'existe pas dans '{0}'. <PERSON><PERSON>z une mise à niveau de votre version de '{0}'.", "This_syntax_requires_an_imported_helper_named_1_with_2_parameters_which_is_not_compatible_with_the_o_2807": "Cette syntaxe nécessite un composant d'assistance importé nommé '{1}' avec {2} paramètres, ce qui n'est pas compatible avec celui qui se trouve dans '{0}'. <PERSON><PERSON>z une mise à niveau de votre version de '{0}'.", "This_type_parameter_might_need_an_extends_0_constraint_2208": "Ce paramètre de type nécessite peut-être une contrainte « extends {0}».", "This_use_of_import_is_invalid_import_calls_can_be_written_but_they_must_have_parentheses_and_cannot__1326": "Cette utilisation de « import » n’est pas valide. Les appels « import() » peuvent être écrits, mais ils doivent avoir des parenthèses et ne peuvent pas avoir d’arguments de type.", "To_convert_this_file_to_an_ECMAScript_module_add_the_field_type_Colon_module_to_0_1482": "Pour convertir ce fichier en module ECMAScript, ajou<PERSON>z le champ `\"type\" : \"module\"` à '{0}'.", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_add_the_field_type_Co_1481": "Pour convertir ce fichier en module ECMAScript, changez son extension de fichier en '{0}', ou ajoutez le champ `\"type\" : \"module\"` à '{1}'.", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_create_a_local_packag_1480": "Pour convertir ce fichier en module ECMAScript, changez son extension de fichier en '{0}' ou créez un fichier package.json local avec `{ \"type\": \"module\" }`.", "To_convert_this_file_to_an_ECMAScript_module_create_a_local_package_json_file_with_type_Colon_module_1483": "Pour convertir ce fichier en module ECMAScript, créez un fichier package.json local avec `{ \"type\": \"module\" }`.", "Top_level_await_expressions_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_n_1378": "Les expressions 'await' de niveau supérieur sont autorisées uniquement lorsque l’option 'module' a la valeur 'es2022', 'esnext', 'system', 'node16' ou 'nodenext' et que l’option 'target' a la valeur 'es2017' ou une valeur supérieure.", "Top_level_declarations_in_d_ts_files_must_start_with_either_a_declare_or_export_modifier_1046": "Les déclarations de niveau supérieur dans les fichiers .d.ts doivent commencer par un modificateur 'declare' ou 'export'.", "Top_level_for_await_loops_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_nod_1432": "Les boucles « for await » de niveau supérieur sont autorisées uniquement lorsque l’option « module » a la valeur « es2022 », « esnext », « system », « node16 » ou « nodenext », et que l’option « target » a la valeur « es2017 » ou une version ultérieure.", "Trailing_comma_not_allowed_1009": "Virgule de fin non autorisée.", "Transpile_each_file_as_a_separate_module_similar_to_ts_transpileModule_6153": "Transpilez chaque fichier sous forme de module distinct (semblable à 'ts.transpileModule').", "Try_npm_i_save_dev_types_Slash_1_if_it_exists_or_add_a_new_declaration_d_ts_file_containing_declare__7035": "Essayez 'npm i --save-dev @types/{1}' s'il existe, ou ajoutez un nouveau fichier de déclaration (.d.ts) contenant 'declare module '{0}';'", "Trying_other_entries_in_rootDirs_6110": "Essai avec d'autres entrées dans 'rootDirs'.", "Trying_substitution_0_candidate_module_location_Colon_1_6093": "<PERSON><PERSON><PERSON> avec la substitution '{0}', emplacement de module candidat : '{1}'.", "Tuple_members_must_all_have_names_or_all_not_have_names_5084": "Les membres de tuples doivent tous avoir des noms ou ne pas en avoir.", "Tuple_type_0_of_length_1_has_no_element_at_index_2_2493": "Le type tuple '{0}' de longueur '{1}' n'a aucun élément à l'index '{2}'.", "Tuple_type_arguments_circularly_reference_themselves_4110": "Les arguments de type tuple se référencent de manière circulaire.", "Type_0_can_only_be_iterated_through_when_using_the_downlevelIteration_flag_or_with_a_target_of_es201_2802": "Le type '{0}' peut être itéré uniquement à l'aide de l'indicateur '--downlevelIteration' ou avec un '--target' dont la valeur est 'es2015' ou une version ultérieure.", "Type_0_cannot_be_used_as_an_index_type_2538": "Impossible d'utiliser le type '{0}' comme type d'index.", "Type_0_cannot_be_used_to_index_type_1_2536": "Le type '{0}' ne peut pas être utilisé pour indexer le type '{1}'.", "Type_0_does_not_satisfy_the_constraint_1_2344": "Le type '{0}' ne satisfait pas la contrainte '{1}'.", "Type_0_does_not_satisfy_the_expected_type_1_1360": "Le type '{0}' ne satisfait pas le type attendu '{1}'.", "Type_0_has_no_call_signatures_2757": "Le type '{0}' n'a aucune signature d'appel.", "Type_0_has_no_construct_signatures_2761": "Le type '{0}' n'a aucune signature de construction.", "Type_0_has_no_matching_index_signature_for_type_1_2537": "Le type '{0}' n'a aucune signature d'index correspondant au type '{1}'.", "Type_0_has_no_properties_in_common_with_type_1_2559": "Le type '{0}' n'a aucune propriété en commun avec le type '{1}'.", "Type_0_has_no_signatures_for_which_the_type_argument_list_is_applicable_2635": "Le type '{0}' n’a aucune signature pour laquelle la liste d’arguments de type est applicable.", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_2739": "Le type '{0}' n'a pas les propriétés suivantes du type '{1}': {2}", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_and_3_more_2740": "Le type '{0}' n'a pas les propriétés suivantes du type '{1}': {2} et de {3} autres.", "Type_0_is_not_a_constructor_function_type_2507": "Le type '{0}' n'est pas un type de fonction constructeur.", "Type_0_is_not_a_valid_async_function_return_type_in_ES5_SlashES3_because_it_does_not_refer_to_a_Prom_1055": "Le type '{0}' n'est pas un type de retour de fonction async valide en ES5/ES3, car il ne référence pas une valeur de constructeur compatible avec une promesse.", "Type_0_is_not_an_array_type_2461": "Le type '{0}' n'est pas un type de tableau.", "Type_0_is_not_an_array_type_or_a_string_type_2495": "Le type '{0}' n'est pas un type de tableau ou un type de chaîne.", "Type_0_is_not_an_array_type_or_a_string_type_or_does_not_have_a_Symbol_iterator_method_that_returns__2549": "Le type '{0}' n'est pas un type tableau ou un type chaîne, ou n'a pas de méthode '[Symbol.iterator]()' qui retourne un itérateur.", "Type_0_is_not_an_array_type_or_does_not_have_a_Symbol_iterator_method_that_returns_an_iterator_2548": "Le type '{0}' n'est pas un type tableau ou n'a pas de méthode '[Symbol.iterator]()' qui retourne un itérateur.", "Type_0_is_not_assignable_to_type_1_2322": "Impossible d'assigner le type '{0}' au type '{1}'.", "Type_0_is_not_assignable_to_type_1_Did_you_mean_2_2820": "Le type '{0}' ne peut pas être attribué au type '{1}'. Voulez-vous dire '{2}'?", "Type_0_is_not_assignable_to_type_1_Two_different_types_with_this_name_exist_but_they_are_unrelated_2719": "Impossible d'assigner le type '{0}' au type '{1}'. Il existe deux types distincts portant ce nom, mais ils ne sont pas liés.", "Type_0_is_not_assignable_to_type_1_as_implied_by_variance_annotation_2636": "Le type «{0}» n’est pas assignable au type «{1}» comme implicite par l’annotation de variance.", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2375": "Le type '{0}' n'est pas assignable au type '{1}' avec 'exactOptionalPropertyTypes : true'. Pensez à ajouter 'undefined' aux types des propriétés de la cible.", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2412": "Le type '{0}' n'est pas assignable au type '{1}' avec 'exactOptionalPropertyTypes : true'. Pensez à ajouter 'undefined' au type de la cible.", "Type_0_is_not_comparable_to_type_1_2678": "Le type '{0}' n'est pas comparable au type '{1}'.", "Type_0_is_not_generic_2315": "Le type '{0}' n'est pas générique.", "Type_0_may_represent_a_primitive_value_which_is_not_permitted_as_the_right_operand_of_the_in_operato_2638": "Le type '{0}' peut représenter une valeur primitive, ce qui n’est pas autorisé en tant qu’opérande droit de l’opérateur 'in'.", "Type_0_must_have_a_Symbol_asyncIterator_method_that_returns_an_async_iterator_2504": "Le type '{0}' doit avoir une méthode '[Symbol.asyncIterator]()' qui retourne un itérateur asynchrone.", "Type_0_must_have_a_Symbol_iterator_method_that_returns_an_iterator_2488": "Le type '{0}' doit avoir une méthode '[Symbol.iterator]()' qui retourne un itérateur.", "Type_0_provides_no_match_for_the_signature_1_2658": "Le type '{0}' ne fournit aucune correspondance pour la signature '{1}'.", "Type_0_recursively_references_itself_as_a_base_type_2310": "Le type '{0}' fait référence à lui-même de manière récursive en tant que type de base.", "Type_Checking_6248": "Vérification du type", "Type_alias_0_circularly_references_itself_2456": "L'alias de type '{0}' fait référence à lui-même de manière circulaire.", "Type_alias_must_be_given_a_name_1439": "Un nom doit être attribué à l’alias de type.", "Type_alias_name_cannot_be_0_2457": "Le nom de l'alias de type ne peut pas être '{0}'.", "Type_aliases_can_only_be_used_in_TypeScript_files_8008": "Les alias de type peuvent uniquement être utilisés dans les fichiers TypeScript.", "Type_annotation_cannot_appear_on_a_constructor_declaration_1093": "Une annotation de type ne peut pas apparaître sur une déclaration de constructeur.", "Type_annotations_can_only_be_used_in_TypeScript_files_8010": "Les annotations de type peuvent uniquement être utilisées dans les fichiers TypeScript.", "Type_argument_expected_1140": "Argument de type attendu.", "Type_argument_list_cannot_be_empty_1099": "La liste des arguments de type ne peut pas être vide.", "Type_arguments_can_only_be_used_in_TypeScript_files_8011": "Les arguments de type peuvent uniquement être utilisés dans les fichiers TypeScript.", "Type_arguments_cannot_be_used_here_1342": "Impossible d'utiliser des arguments de type ici.", "Type_arguments_for_0_circularly_reference_themselves_4109": "Les arguments de type pour '{0}' se référencent de manière circulaire.", "Type_assertion_expressions_can_only_be_used_in_TypeScript_files_8016": "Les expressions d'assertion de type peuvent uniquement être utilisées dans les fichiers TypeScript.", "Type_at_position_0_in_source_is_not_compatible_with_type_at_position_1_in_target_2626": "Le type situé à la position {0} dans la source n'est pas compatible avec le type situé à la position {1} dans la cible.", "Type_at_positions_0_through_1_in_source_is_not_compatible_with_type_at_position_2_in_target_2627": "Le type situé aux positions allant de {0} à {1} dans la source n'est pas compatible avec le type situé à la position {2} dans la cible.", "Type_declaration_files_to_be_included_in_compilation_6124": "Fichiers de déclaration de type à inclure dans la compilation.", "Type_expected_1110": "Type attendu.", "Type_import_assertions_should_have_exactly_one_key_resolution_mode_with_value_import_or_require_1456": "Les assertions d’importation de type doivent avoir exactement une clé ( « mode résolution » ) avec la valeur « import » ou « require ».", "Type_instantiation_is_excessively_deep_and_possibly_infinite_2589": "L'instanciation de type est trop profonde et éventuellement infinie.", "Type_is_referenced_directly_or_indirectly_in_the_fulfillment_callback_of_its_own_then_method_1062": "Le type est directement ou indirectement référencé dans le rappel d'exécution de sa propre méthode 'then'.", "Type_library_referenced_via_0_from_file_1_1402": "Bibliothèque de types référencée via '{0}' à partir du fichier '{1}'", "Type_library_referenced_via_0_from_file_1_with_packageId_2_1403": "Bibliothèque de types référencée via '{0}' à partir du fichier '{1}' ayant le packageId '{2}'", "Type_of_await_operand_must_either_be_a_valid_promise_or_must_not_contain_a_callable_then_member_1320": "Le type d'un opérande 'await' doit être une promesse valide ou ne doit contenir aucun membre 'then' pouvant être appelé.", "Type_of_computed_property_s_value_is_0_which_is_not_assignable_to_type_1_2418": "Le type de la valeur de la propriété calculée est '{0}'. Il ne peut pas être assigné au type '{1}'.", "Type_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2844": "Le type de variable membre d’instance '{0}' ne peut pas référencer l’identificateur '{1}' déclaré dans le constructeur.", "Type_of_iterated_elements_of_a_yield_Asterisk_operand_must_either_be_a_valid_promise_or_must_not_con_1322": "Le type des éléments itérés d'un opérande 'yield*' doit être une promesse valide ou ne doit contenir aucun membre 'then' pouvant être appelé.", "Type_of_property_0_circularly_references_itself_in_mapped_type_1_2615": "Le type de la propriété '{0}' se référence de façon circulaire dans le type mappé '{1}'.", "Type_of_yield_operand_in_an_async_generator_must_either_be_a_valid_promise_or_must_not_contain_a_cal_1321": "Le type d'un opérande 'yield' dans un générateur asynchrone doit être une promesse valide ou ne doit contenir aucun membre 'then' pouvant être appelé.", "Type_originates_at_this_import_A_namespace_style_import_cannot_be_called_or_constructed_and_will_cau_7038": "Le type provient de cette importation. Impossible d'appeler ou de construire une importation de style d'espace de noms, ce qui va entraîner un échec au moment de l'exécution. À la place, utilisez ici une importation par défaut ou une importation avec require.", "Type_parameter_0_has_a_circular_constraint_2313": "Le paramètre de type '{0}' possède une contrainte circulaire.", "Type_parameter_0_has_a_circular_default_2716": "Le paramètre de type '{0}' a une valeur par défaut circulaire.", "Type_parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4008": "Le paramètre de type '{0}' de la signature d'appel de l'interface exportée possède ou utilise le nom privé '{1}'.", "Type_parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4006": "Le paramètre de type '{0}' de la signature de constructeur de l'interface exportée possède ou utilise le nom privé '{1}'.", "Type_parameter_0_of_exported_class_has_or_is_using_private_name_1_4002": "Le paramètre de type '{0}' de la classe exportée possède ou utilise le nom privé '{1}'.", "Type_parameter_0_of_exported_function_has_or_is_using_private_name_1_4016": "Le paramètre de type '{0}' de la fonction exportée possède ou utilise le nom privé '{1}'.", "Type_parameter_0_of_exported_interface_has_or_is_using_private_name_1_4004": "Le paramètre de type '{0}' de l'interface exportée possède ou utilise le nom privé '{1}'.", "Type_parameter_0_of_exported_mapped_object_type_is_using_private_name_1_4103": "Le paramètre de type '{0}' du type d'objet mappé exporté utilise le nom privé '{1}'.", "Type_parameter_0_of_exported_type_alias_has_or_is_using_private_name_1_4083": "Le paramètre de type '{0}' de l'alias du type exporté contient ou utilise le nom privé '{1}'.", "Type_parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4014": "Le paramètre de type '{0}' de la méthode de l'interface exportée possède ou utilise le nom privé '{1}'.", "Type_parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4012": "Le paramètre de type '{0}' de la méthode publique de la classe exportée possède ou utilise le nom privé '{1}'.", "Type_parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4010": "Le paramètre de type '{0}' de la méthode statique publique de la classe exportée possède ou utilise le nom privé '{1}'.", "Type_parameter_declaration_expected_1139": "Déclaration du paramètre de type attendue.", "Type_parameter_declarations_can_only_be_used_in_TypeScript_files_8004": "Les déclarations de paramètre de type peuvent uniquement être utilisées dans les fichiers TypeScript.", "Type_parameter_defaults_can_only_reference_previously_declared_type_parameters_2744": "Les valeurs par défaut des paramètres de type peuvent uniquement référencer des paramètres de type déclarés.", "Type_parameter_list_cannot_be_empty_1098": "La liste des paramètres de type ne peut pas être vide.", "Type_parameter_name_cannot_be_0_2368": "Le nom du paramètre de type ne peut pas être '{0}'.", "Type_parameters_cannot_appear_on_a_constructor_declaration_1092": "Les paramètres de type ne peuvent pas apparaître sur une déclaration de constructeur.", "Type_predicate_0_is_not_assignable_to_1_1226": "Impossible d'assigner le prédicat de type '{0}' à '{1}'.", "Type_produces_a_tuple_type_that_is_too_large_to_represent_2799": "Le type produit un type de tuple trop grand pour être représenté.", "Type_reference_directive_0_was_not_resolved_6120": "======== La directive de référence de type '{0}' n'a pas été résolue. ========", "Type_reference_directive_0_was_successfully_resolved_to_1_primary_Colon_2_6119": "======== La directive de référence de type '{0}' a été correctement résolue en '{1}', primaire : {2}. ========", "Type_reference_directive_0_was_successfully_resolved_to_1_with_Package_ID_2_primary_Colon_3_6219": "======== La directive de référence de type '{0}' a été correctement résolue en '{1}' avec l'ID de package '{2}', primaire : {3}. ========", "Type_satisfaction_expressions_can_only_be_used_in_TypeScript_files_8037": "Les expressions de satisfaction de type peuvent uniquement être utilisées dans les fichiers TypeScript.", "Types_cannot_appear_in_export_declarations_in_JavaScript_files_18043": "Les types ne peuvent pas apparaître dans les déclarations d’exportation dans les fichiers JavaScript.", "Types_have_separate_declarations_of_a_private_property_0_2442": "Les types ont des déclarations distinctes d'une propriété privée '{0}'.", "Types_of_construct_signatures_are_incompatible_2419": "Les types de signature de construction sont incompatibles.", "Types_of_parameters_0_and_1_are_incompatible_2328": "Les types des paramètres '{0}' et '{1}' sont incompatibles.", "Types_of_property_0_are_incompatible_2326": "Les types de la propriété '{0}' sont incompatibles.", "Unable_to_open_file_0_6050": "Impossible d'ouvrir le fichier '{0}'.", "Unable_to_resolve_signature_of_class_decorator_when_called_as_an_expression_1238": "Impossible de résoudre la signature d'un élément décoratif de classe quand il est appelé en tant qu'expression.", "Unable_to_resolve_signature_of_method_decorator_when_called_as_an_expression_1241": "Impossible de résoudre la signature d'un élément décoratif de méthode quand il est appelé en tant qu'expression.", "Unable_to_resolve_signature_of_parameter_decorator_when_called_as_an_expression_1239": "Impossible de résoudre la signature d'un élément décoratif de paramètre quand il est appelé en tant qu'expression.", "Unable_to_resolve_signature_of_property_decorator_when_called_as_an_expression_1240": "Impossible de résoudre la signature d'un élément décoratif de propriété quand il est appelé en tant qu'expression.", "Unexpected_end_of_text_1126": "Fin de texte inattendue.", "Unexpected_keyword_or_identifier_1434": "<PERSON>t clé ou identificateur inattendu.", "Unexpected_token_1012": "<PERSON>on inattendu.", "Unexpected_token_A_constructor_method_accessor_or_property_was_expected_1068": "Jeton inattendu. Un constructeur, une méthode, un accesseur ou une propriété est attendu.", "Unexpected_token_A_type_parameter_name_was_expected_without_curly_braces_1069": "Jeton inattendu. Un nom de paramètre de type est attendu sans accolades.", "Unexpected_token_Did_you_mean_or_gt_1382": "Jeton inattendu. Est-ce que vous avez voulu utiliser '{'>'}' ou '&gt;' ?", "Unexpected_token_Did_you_mean_or_rbrace_1381": "Jeton inattendu. Est-ce que vous avez voulu utiliser '{'}'}' ou '&rbrace;' ?", "Unexpected_token_expected_1179": "<PERSON>on inattendu. '{' est attendu.", "Unknown_build_option_0_5072": "Option de build inconnue : '{0}'.", "Unknown_build_option_0_Did_you_mean_1_5077": "Option de build inconnue : '{0}'. Est-ce que vous avez voulu utiliser '{1}' ?", "Unknown_compiler_option_0_5023": "Option de compilateur '{0}' inconnue.", "Unknown_compiler_option_0_Did_you_mean_1_5025": "Option de compilateur inconnue : '{0}'. Est-ce que vous avez voulu utiliser '{1}' ?", "Unknown_keyword_or_identifier_Did_you_mean_0_1435": "<PERSON>t clé ou identificateur inconnu. Souhaitiez-vous utiliser «{0}» ?", "Unknown_option_excludes_Did_you_mean_exclude_6114": "Option 'excludes' inconnue. Voulez-vous utiliser 'exclude' ?", "Unknown_type_acquisition_option_0_17010": "Option d'acquisition de type inconnue '{0}'.", "Unknown_type_acquisition_option_0_Did_you_mean_1_17018": "Option d'acquisition de type inconnue : '{0}'. Est-ce que vous avez voulu utiliser '{1}' ?", "Unknown_watch_option_0_5078": "Option de surveillance inconnue : '{0}'.", "Unknown_watch_option_0_Did_you_mean_1_5079": "Option de surveillance inconnue : '{0}'. Est-ce que vous avez voulu utiliser '{1}' ?", "Unreachable_code_detected_7027": "Code inatteignable détecté.", "Unterminated_Unicode_escape_sequence_1199": "Séquence d'échappement Unicode inachevée.", "Unterminated_quoted_string_in_response_file_0_6045": "<PERSON><PERSON><PERSON> entre guillemets inachevée dans le fichier réponse '{0}'.", "Unterminated_regular_expression_literal_1161": "Littéral d'expression régulière inachevé.", "Unterminated_string_literal_1002": "Littéral de chaîne in<PERSON>.", "Unterminated_template_literal_1160": "Littéral de modèle inachevé.", "Untyped_function_calls_may_not_accept_type_arguments_2347": "Les appels de fonctions non typées ne peuvent pas accepter d'arguments de type.", "Unused_label_7028": "Étiquette inutilisée.", "Unused_ts_expect_error_directive_2578": "Directive '@ts-expect-error' inutilisée.", "Update_import_from_0_90058": "Mettre à jour l’importation à partir de \"{0}\"", "Updating_output_of_project_0_6373": "Mise à jour de la sortie du projet '{0}'...", "Updating_output_timestamps_of_project_0_6359": "Mise à jour des horodatages de sortie du projet '{0}'...", "Updating_unchanged_output_timestamps_of_project_0_6371": "Mise à jour des horodatages de sortie inchangés du projet '{0}'...", "Use_0_95174": "Utilisez `{0}`.", "Use_Number_isNaN_in_all_conditions_95175": "Utilisez 'Number.isNaN' dans toutes les conditions.", "Use_element_access_for_0_95145": "Utiliser l'accès à l'élément pour '{0}'", "Use_element_access_for_all_undeclared_properties_95146": "L'accès à l'élément est utilisé pour toutes les propriétés non déclarées.", "Use_synthetic_default_member_95016": "Utilisez un membre 'default' synthétique.", "Using_0_subpath_1_with_target_2_6404": "Utilisation de '{0}' de sous-chemin '{1}' avec la cible '{2}'.", "Using_a_string_in_a_for_of_statement_is_only_supported_in_ECMAScript_5_and_higher_2494": "L'utilisation d'une chaîne dans une instruction 'for...of' est prise en charge uniquement dans ECMAScript 5 et version supérieure.", "Using_build_b_will_make_tsc_behave_more_like_a_build_orchestrator_than_a_compiler_This_is_used_to_tr_6915": "L’utilisation de--build,-b fera en sorte que tsc se comporte plus comme une build orchestrateur qu’un compilateur. Utilisé pour déclencher la génération de projets composites sur lesquels vous pouvez obtenir des informations supplémentaires sur {0}", "Using_compiler_options_of_project_reference_redirect_0_6215": "Utilisation des options de compilateur de la redirection de référence de projet : '{0}'.", "VERSION_6036": "VERSION", "Value_of_type_0_has_no_properties_in_common_with_type_1_Did_you_mean_to_call_it_2560": "La valeur de type '{0}' n'a aucune propriété en commun avec le type '{1}'. Voulez-vous vraiment l'appeler ?", "Value_of_type_0_is_not_callable_Did_you_mean_to_include_new_2348": "La valeur de type '{0}' ne peut pas être appelée. Voulez-vous inclure 'new' ?", "Variable_0_implicitly_has_an_1_type_7005": "La variable '{0}' possède implicitement un type '{1}'.", "Variable_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7043": "La variable '{0}' a implicitement un type '{1}', mais il est possible de déduire un meilleur type à partir de l'utilisation.", "Variable_0_implicitly_has_type_1_in_some_locations_but_a_better_type_may_be_inferred_from_usage_7046": "La variable '{0}' a implicitement le type '{1}' à certains emplacements, mais il est possible de déduire un meilleur type à partir de l'utilisation.", "Variable_0_implicitly_has_type_1_in_some_locations_where_its_type_cannot_be_determined_7034": "La variable '{0}' a implicitement le type '{1}' dans certains emplacements où son type ne peut pas être déterminé.", "Variable_0_is_used_before_being_assigned_2454": "La variable '{0}' est utilisée avant d'être assignée.", "Variable_declaration_expected_1134": "Déclaration de variable attendue.", "Variable_declaration_list_cannot_be_empty_1123": "La liste des déclarations de variable ne peut pas être vide.", "Variable_declaration_not_allowed_at_this_location_1440": "Déclaration de variable non autorisée à cet emplacement.", "Variadic_element_at_position_0_in_source_does_not_match_element_at_position_1_in_target_2625": "L'élément variadique situé à la position {0} dans la source ne correspond pas à l'élément situé à la position {1} dans la cible.", "Variance_annotations_are_only_supported_in_type_aliases_for_object_function_constructor_and_mapped_t_2637": "Les annotations de variance sont uniquement prises en charge dans les alias de type pour les types objet, fonction, constructeur et mappé.", "Version_0_6029": "Version {0}", "Visit_https_Colon_Slash_Slashaka_ms_Slashtsconfig_to_read_more_about_this_file_95110": "Visitez https://aka.ms/tsconfig pour en savoir plus sur ce fichier", "WATCH_OPTIONS_6918": "OPTIONS D’OBSERVATION", "Watch_and_Build_Modes_6250": "Modes d’Observation et de Génération", "Watch_input_files_6005": "Fichiers d'entrée d'espion.", "Watch_option_0_requires_a_value_of_type_1_5080": "L'option de surveillance '{0}' nécessite une valeur de type {1}.", "We_can_only_write_a_type_for_0_by_adding_a_type_for_the_entire_parameter_here_2843": "Nous ne pouvons écrire un type pour « {0} » qu’en ajoutant un type pour l’ensemble du paramètre ici.", "When_assigning_functions_check_to_ensure_parameters_and_the_return_values_are_subtype_compatible_6698": "Lors de l’attribution de fonctions, vérifiez que les paramètres et les valeurs de retour sont compatibles avec le sous-type.", "When_type_checking_take_into_account_null_and_undefined_6699": "Lors de la vérification de type, prenez en compte « null » et « undefined ».", "Whether_to_keep_outdated_console_output_in_watch_mode_instead_of_clearing_the_screen_6191": "Garder la sortie de console obsolète en mode espion au lieu d'effacer l'écran.", "Wrap_all_invalid_characters_in_an_expression_container_95109": "Inclure dans un wrapper tous les caractères non valides au sein d'un conteneur d'expressions", "Wrap_all_object_literal_with_parentheses_95116": "Placer tous les littéraux d'objet entre parenthèses", "Wrap_all_unparented_JSX_in_JSX_fragment_95121": "Inclure dans un wrapper tous les JSX non apparentés au sein d'un fragment JSX", "Wrap_in_JSX_fragment_95120": "Inclure dans un wrapper au sein d'un fragment JSX", "Wrap_invalid_character_in_an_expression_container_95108": "Inclure dans un wrapper un caractère non valide au sein d'un conteneur d'expressions", "Wrap_the_following_body_with_parentheses_which_should_be_an_object_literal_95113": "Placer le corps suivant entre parenthèses pour indiquer qu'il s'agit d'un littéral d'objet", "You_can_learn_about_all_of_the_compiler_options_at_0_6913": "Vous pouvez en savoir plus sur toutes les options du compilateur sur {0}", "You_cannot_rename_a_module_via_a_global_import_8031": "Vous ne pouvez pas renommer un module via une importation globale.", "You_cannot_rename_elements_that_are_defined_in_a_node_modules_folder_8035": "Vous ne pouvez pas renommer les éléments définis dans un dossier « node_modules ».", "You_cannot_rename_elements_that_are_defined_in_another_node_modules_folder_8036": "Vous ne pouvez pas renommer les éléments définis dans un autre dossier « node_modules ».", "You_cannot_rename_elements_that_are_defined_in_the_standard_TypeScript_library_8001": "Vous ne pouvez pas renommer des éléments définis dans la bibliothèque TypeScript standard.", "You_cannot_rename_this_element_8000": "Vous ne pouvez pas renommer cet élément.", "_0_accepts_too_few_arguments_to_be_used_as_a_decorator_here_Did_you_mean_to_call_it_first_and_write__1329": "'{0}' accepte trop peu d'arguments pour pouvoir être utilisé ici en tant qu'élément décoratif. Voulez-vous vraiment l'appeler d'abord et écrire '@{0}()' ?", "_0_and_1_index_signatures_are_incompatible_2330": "Les signatures d'index « {0} » et « {1} » sont incompatibles.", "_0_and_1_operations_cannot_be_mixed_without_parentheses_5076": "Les opérations '{0}' et '{1}' ne peuvent pas être mélangées sans parenthèses.", "_0_are_specified_twice_The_attribute_named_0_will_be_overwritten_2710": "'{0}' spé<PERSON><PERSON><PERSON> deux fois. L'attribut nommé '{0}' va être remplacé.", "_0_can_only_be_imported_by_turning_on_the_esModuleInterop_flag_and_using_a_default_import_2596": "'{0}' peut uniquement être importé via l'activation de l'indicateur 'esModuleInterop' et l'utilisation d'une importation par défaut.", "_0_can_only_be_imported_by_using_a_default_import_2595": "'{0}' peut uniquement être importé via l'utilisation d'une importation par défaut.", "_0_can_only_be_imported_by_using_a_require_call_or_by_turning_on_the_esModuleInterop_flag_and_using__2598": "'{0}' peut uniquement être importé à l'aide d'un appel 'require' ou via l'activation de l'indicateur 'esModuleInterop' et l'utilisation d'une importation par défaut.", "_0_can_only_be_imported_by_using_a_require_call_or_by_using_a_default_import_2597": "'{0}' peut uniquement être importé à l'aide d'un appel 'require' ou via l'utilisation d'une importation par défaut.", "_0_can_only_be_imported_by_using_import_1_require_2_or_a_default_import_2616": "'{0}' peut uniquement être importé à l'aide de 'import {1} = require({2})' ou via l'utilisation d'une importation par défaut.", "_0_can_only_be_imported_by_using_import_1_require_2_or_by_turning_on_the_esModuleInterop_flag_and_us_2617": "'{0}' peut uniquement être importé à l'aide de 'import {1} = require({2})' ou via l'activation de l'indicateur 'esModuleInterop' et l'utilisation d'une importation par défaut.", "_0_cannot_be_compiled_under_isolatedModules_because_it_is_considered_a_global_script_file_Add_an_imp_1208": "Impossible de compiler '{0}' sous '--isolatedModules', car il est considéré comme un fichier de script global. Ajoutez une importation, une exportation ou une instruction 'export {}' vide pour en faire un module.", "_0_cannot_be_used_as_a_JSX_component_2786": "Impossible d'utiliser '{0}' comme composant JSX.", "_0_cannot_be_used_as_a_value_because_it_was_exported_using_export_type_1362": "'{0}' ne peut pas être utilisé en tant que valeur, car il a été exporté à l'aide de 'export type'.", "_0_cannot_be_used_as_a_value_because_it_was_imported_using_import_type_1361": "'{0}' ne peut pas être utilisé en tant que valeur, car il a été importé à l'aide de 'import type'.", "_0_components_don_t_accept_text_as_child_elements_Text_in_JSX_has_the_type_string_but_the_expected_t_2747": "Les composants '{0}' n'acceptent pas du texte en tant qu'éléments enfants. Le texte dans JSX a le type 'string', mais le type attendu de '{1}' est '{2}'.", "_0_could_be_instantiated_with_an_arbitrary_type_which_could_be_unrelated_to_1_5082": "'{0}' a pu être instancié avec un type arbitraire qui n'est peut-être pas lié à '{1}'.", "_0_declarations_can_only_be_used_in_TypeScript_files_8006": "Les déclarations '{0}' peuvent uniquement être utilisées dans les fichiers TypeScript.", "_0_expected_1005": "'{0}' attendu.", "_0_has_no_exported_member_named_1_Did_you_mean_2_2724": "'{0}' n'a aucun membre exporté nommé '{1}'. Est-ce que vous pensiez à '{2}' ?", "_0_implicitly_has_an_1_return_type_but_a_better_type_may_be_inferred_from_usage_7050": "'{0}' a implicitement un type de retour '{1}', mais il est possible de déduire un meilleur type à partir de l'utilisation.", "_0_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_reference_7023": "'{0}' possède implicitement le type de retour 'any', car il n'a pas d'annotation de type de retour, et est référencé directement ou indirectement dans l'une de ses expressions de retour.", "_0_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_and_is_referenced_directly_or__7022": "'{0}' a implicitement le type 'any', car il n'a pas d'annotation de type et est référencé directement ou indirectement dans son propre initialiseur.", "_0_index_signatures_are_incompatible_2634": "Les signatures d'index « {0} » sont incompatibles.", "_0_index_type_1_is_not_assignable_to_2_index_type_3_2413": "Le type d’index « {0} », « {1} », ne peut pas être attribué au type d’index « {2} », « {3} ».", "_0_is_a_primitive_but_1_is_a_wrapper_object_Prefer_using_0_when_possible_2692": "'{0}' est une primitive, mais '{1}' est un objet wrapper. Si possible, utilisez '{0}' de préférence.", "_0_is_a_type_and_cannot_be_imported_in_JavaScript_files_Use_1_in_a_JSDoc_type_annotation_18042": "'{0}' est un type qui ne peut pas être importé dans des fichiers JavaScript. Utilisez '{1}' dans une annotation de type JSDoc.", "_0_is_a_type_and_must_be_imported_using_a_type_only_import_when_preserveValueImports_and_isolatedMod_1444": "« {0} » est un type seul et doit être importé à l'aide d'une importation de type seul lorsque « preserveValueImports » et « isolatedModules » sont tous deux activés.", "_0_is_an_unused_renaming_of_1_Did_you_intend_to_use_it_as_a_type_annotation_2842": "« {0} » est un changement de nom inutilisé de « {1} ». Souhaitiez-vous l’utiliser comme annotation de type?", "_0_is_assignable_to_the_constraint_of_type_1_but_1_could_be_instantiated_with_a_different_subtype_of_5075": "'{0}' peut être assigné à la contrainte de type '{1}', mais '{1}' a pu être instancié avec un autre sous-type de contrainte '{2}'.", "_0_is_automatically_exported_here_18044": "'{0}' est automatiquement exporté ici.", "_0_is_declared_but_its_value_is_never_read_6133": "'{0}' est déclaré mais sa valeur n'est jamais lue.", "_0_is_declared_but_never_used_6196": "'{0}' est déclaré mais n'est jamais utilisé.", "_0_is_declared_here_2728": "'{0}' est déclaré ici.", "_0_is_defined_as_a_property_in_class_1_but_is_overridden_here_in_2_as_an_accessor_2611": "'{0}' est défini en tant que propriété dans la classe '{1}', mais il est remplacé ici dans '{2}' en tant qu'accesseur.", "_0_is_defined_as_an_accessor_in_class_1_but_is_overridden_here_in_2_as_an_instance_property_2610": "'{0}' est défini en tant qu'accesseur dans la classe '{1}', mais il est remplacé ici dans '{2}' en tant que propriété d'instance.", "_0_is_deprecated_6385": "'{0}' est déprécié.", "_0_is_not_a_valid_meta_property_for_keyword_1_Did_you_mean_2_17012": "'{0}' n'est pas une métapropriété valide pour le mot clé '{1}'. Est-ce qu'il ne s'agit pas plutôt de '{2}' ?", "_0_is_not_allowed_as_a_parameter_name_1390": "'{0}' n'est pas autorisé comme nom de paramètre.", "_0_is_not_allowed_as_a_variable_declaration_name_1389": "'{0}' n'est pas autorisé en tant que nom de déclaration de variable.", "_0_is_of_type_unknown_18046": "'{0}' est de type 'unknown'.", "_0_is_possibly_null_18047": "'{0}' est peut-être 'null'.", "_0_is_possibly_null_or_undefined_18049": "'{0}' est peut-être 'null' ou 'undefined'.", "_0_is_possibly_undefined_18048": "'{0}' est peut-être 'non défini'.", "_0_is_referenced_directly_or_indirectly_in_its_own_base_expression_2506": "'{0}' est référencé directement ou indirectement dans sa propre expression de base.", "_0_is_referenced_directly_or_indirectly_in_its_own_type_annotation_2502": "'{0}' est référencé directement ou indirectement dans sa propre annotation de type.", "_0_is_specified_more_than_once_so_this_usage_will_be_overwritten_2783": "'{0}' est spécifié plusieurs fois. Cette utilisation va donc être remplacée.", "_0_list_cannot_be_empty_1097": "La liste '{0}' ne peut pas être vide.", "_0_modifier_already_seen_1030": "Modificateur '{0}' d<PERSON><PERSON><PERSON> rencontré.", "_0_modifier_can_only_appear_on_a_type_parameter_of_a_class_interface_or_type_alias_1274": "Le modificateur «{0}» ne peut apparaître que sur un paramètre de type d’une classe, d’une interface ou d’un alias de type", "_0_modifier_cannot_appear_on_a_constructor_declaration_1089": "Le modificateur '{0}' ne peut pas apparaître sur une déclaration de constructeur.", "_0_modifier_cannot_appear_on_a_module_or_namespace_element_1044": "Le modificateur '{0}' ne peut pas apparaître dans un élément de module ou d'espace de noms.", "_0_modifier_cannot_appear_on_a_parameter_1090": "Le modificateur '{0}' ne peut pas apparaître dans un paramètre.", "_0_modifier_cannot_appear_on_a_type_member_1070": "Le modificateur '{0}' ne peut pas apparaître dans un membre de type.", "_0_modifier_cannot_appear_on_a_type_parameter_1273": "Le modificateur «{0}» ne peut pas apparaître sur un paramètre de type", "_0_modifier_cannot_appear_on_an_index_signature_1071": "Le modificateur '{0}' ne peut pas apparaître dans une signature d'index.", "_0_modifier_cannot_appear_on_class_elements_of_this_kind_1031": "Le modificateur '{0}' ne peut pas apparaître sur les éléments de classe de ce genre.", "_0_modifier_cannot_be_used_here_1042": "Impossible d'utiliser le modificateur '{0}' ici.", "_0_modifier_cannot_be_used_in_an_ambient_context_1040": "Impossible d'utiliser le modificateur '{0}' dans un contexte ambiant.", "_0_modifier_cannot_be_used_with_1_modifier_1243": "Impossible d'utiliser les modificateurs '{0}' et '{1}' ensemble.", "_0_modifier_cannot_be_used_with_a_private_identifier_18019": "Le modificateur '{0}' ne peut pas être utilisé avec un identificateur privé.", "_0_modifier_must_precede_1_modifier_1029": "Le modificateur '{0}' doit précéder le modificateur '{1}'.", "_0_needs_an_explicit_type_annotation_2782": "'{0}' a besoin d'une annotation de type explicite.", "_0_only_refers_to_a_type_but_is_being_used_as_a_namespace_here_2702": "'{0}' référence uniquement un type mais s'utilise en tant qu'espace de noms ici.", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_2693": "'{0}' fait uniquement référence à un type mais s'utilise en tant que valeur ici.", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Did_you_mean_to_use_1_in_0_2690": "'{0}' fait uniquement référence à un type, mais il est utilisé ici en tant que valeur. Voulez-vous vraiment utiliser '{1} dans {0}' ?", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Do_you_need_to_change_your_target_library_2585": "'{0}' fait uniquement référence à un type, mais il est utilisé ici en tant que valeur. Devez-vous changer votre bibliothèque cible ? Essayez de remplacer l'option de compilateur 'lib' par es2015 ou une version ultérieure.", "_0_refers_to_a_UMD_global_but_the_current_file_is_a_module_Consider_adding_an_import_instead_2686": "'{0}' fait référence à une variable globale UMD, mais le fichier actuel est un module. Ajoutez une importation à la place.", "_0_refers_to_a_value_but_is_being_used_as_a_type_here_Did_you_mean_typeof_0_2749": "'{0}' fait référence à une valeur, mais il est utilisé ici en tant que type. Est-ce que vous avez voulu utiliser 'typeof {0}' ?", "_0_resolves_to_a_type_only_declaration_and_must_be_imported_using_a_type_only_import_when_preserveVa_1446": "« {0} » se résout en une déclaration de type seul et doit être importé à l'aide d'une importation de type seul lorsque « preserveValueImports » et « isolatedModules » sont tous deux activés.", "_0_resolves_to_a_type_only_declaration_and_must_be_re_exported_using_a_type_only_re_export_when_isol_1448": "« {0} » se résout une déclaration de type unique et doit être réexporté à l'aide d'une réexportation de type unique lorsque l'option « isolatedModules » est activée.", "_0_should_be_set_inside_the_compilerOptions_object_of_the_config_json_file_6258": "'{0}' doit être défini dans l'objet 'compilerOptions' du fichier de configuration json", "_0_tag_already_specified_1223": "La balise '{0}' est déjà spécifiée.", "_0_was_also_declared_here_6203": "'{0}' a également été déclaré ici.", "_0_was_exported_here_1377": "'{0}' a été exporté ici.", "_0_was_imported_here_1376": "'{0}' a été importé ici.", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_return_type_7010": "'{0}', qui ne dispose pas d'annotation de type de retour, possède implicitement un type de retour '{1}'.", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_yield_type_7055": "'{0}', qui n'a pas d'annotation de type de retour, a implicitement le type de retour '{1}'.", "abstract_modifier_can_only_appear_on_a_class_method_or_property_declaration_1242": "Le modificateur 'abstract' peut apparaître uniquement dans une déclaration de classe, de méthode ou de propriété.", "accessor_modifier_can_only_appear_on_a_property_declaration_1275": "Le modificateur 'accessor' ne peut apparaître que sur une déclaration de propriété.", "and_here_6204": "et ici.", "arguments_cannot_be_referenced_in_property_initializers_2815": "Les « arguments » ne peuvent pas être référencés dans les initialiseurs de propriété.", "auto_Colon_Treat_files_with_imports_exports_import_meta_jsx_with_jsx_Colon_react_jsx_or_esm_format_w_1476": "« auto » : traitez les fichiers avec des importations, des exportations, import.meta, jsx (avec jsx: react-jsx) ou un format esm (avec module : node16+) en tant que modules.", "await_expressions_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_fi_1375": "Les expressions 'await' sont uniquement autorisées au niveau supérieur d'un fichier quand celui-ci est un module, mais le fichier actuel n'a pas d'importations ou d'exportations. Ajoutez un 'export {}' vide pour faire de ce fichier un module.", "await_expressions_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1308": "Les expressions 'await' sont autorisées uniquement dans les fonctions asynchrones et aux niveaux supérieurs des modules.", "await_expressions_cannot_be_used_in_a_parameter_initializer_2524": "Impossible d'utiliser des expressions 'await' dans un initialiseur de paramètre.", "await_has_no_effect_on_the_type_of_this_expression_80007": "'await' n'a aucun effet sur le type de cette expression.", "baseUrl_option_is_set_to_0_using_this_value_to_resolve_non_relative_module_name_1_6106": "L'option 'baseUrl' a la valeur '{0}'. Utilisation de cette valeur pour la résolution du nom de module non relatif '{1}'.", "can_only_be_used_at_the_start_of_a_file_18026": "'#!' peut uniquement être utilisé au début d'un fichier.", "case_or_default_expected_1130": "'case' ou 'default' attendu.", "catch_or_finally_expected_1472": "« Catch » ou « finally » attendu.", "const_declarations_can_only_be_declared_inside_a_block_1156": "Les déclarations 'const' ne peuvent être déclarées que dans un bloc.", "const_declarations_must_be_initialized_1155": "Les déclarations 'const' doivent être initialisées.", "const_enum_member_initializer_was_evaluated_to_a_non_finite_value_2477": "L'initialiseur de membre enum 'const' donne une valeur non finie.", "const_enum_member_initializer_was_evaluated_to_disallowed_value_NaN_2478": "L'initialiseur de membre enum 'const' donne une valeur non autorisée 'NaN'.", "const_enum_member_initializers_can_only_contain_literal_values_and_other_computed_enum_values_2474": "Les initialiseurs de membres const enum peuvent uniquement contenir des valeurs littérales et autres valeurs enum calculées.", "const_enums_can_only_be_used_in_property_or_index_access_expressions_or_the_right_hand_side_of_an_im_2475": "Les enums 'const' ne peuvent être utilisés que dans les expressions d'accès à une propriété ou un index, ou dans la partie droite d'une déclaration d'importation, d'une assignation d'exportation ou d'une requête de type.", "constructor_cannot_be_used_as_a_parameter_property_name_2398": "'constructor' ne peut pas être utilisé en tant que nom de propriété de paramètre.", "constructor_is_a_reserved_word_18012": "'#constructor' est un mot réservé.", "default_Colon_6903": "Par <PERSON><PERSON><PERSON> :", "delete_cannot_be_called_on_an_identifier_in_strict_mode_1102": "'delete' ne peut pas être appelé dans un identificateur en mode strict.", "export_Asterisk_does_not_re_export_a_default_1195": "'export *' ne réexporte pas d'exportations par défaut.", "export_can_only_be_used_in_TypeScript_files_8003": "'export =' peut uniquement être utilisé dans les fichiers TypeScript.", "export_modifier_cannot_be_applied_to_ambient_modules_and_module_augmentations_since_they_are_always__2668": "Impossible d'appliquer le modificateur 'export' aux modules ambients et aux augmentations de module, car ils sont toujours visibles.", "extends_clause_already_seen_1172": "Clause 'extends' dé<PERSON><PERSON> rencontrée.", "extends_clause_must_precede_implements_clause_1173": "La clause 'extends' doit précéder la clause 'implements'.", "extends_clause_of_exported_class_0_has_or_is_using_private_name_1_4020": "La clause 'extends' de la classe exportée '{0}' comporte ou utilise le nom privé '{1}'.", "extends_clause_of_exported_class_has_or_is_using_private_name_0_4021": "La clause 'extends' de la classe exportée comporte ou utilise le nom privé '{0}'.", "extends_clause_of_exported_interface_0_has_or_is_using_private_name_1_4022": "La clause 'extends' de l'interface exportée '{0}' comporte ou utilise le nom privé '{1}'.", "false_unless_composite_is_set_6906": "« false », sauf si « composite » est défini", "false_unless_strict_is_set_6905": "« false », sauf si « strict » est défini", "file_6025": "<PERSON><PERSON><PERSON>", "for_await_loops_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_file_1431": "Les boucles 'for await' sont uniquement autorisées au niveau supérieur d'un fichier quand celui-ci est un module, mais le fichier actuel n'a aucune importation ou exportation. Ajoutez un 'export {}' vide pour faire de ce fichier un module.", "for_await_loops_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1103": "Les boucles 'for await' sont autorisées uniquement dans les fonctions asynchrones et aux niveaux supérieurs des modules.", "get_and_set_accessors_cannot_declare_this_parameters_2784": "Les accesseurs 'get' et 'set' ne peuvent pas déclarer les paramètres 'this'.", "if_files_is_specified_otherwise_Asterisk_Asterisk_Slash_Asterisk_6908": "«[]» si « files » est spécifié, sinon « [\"**/*\"]5D; »", "implements_clause_already_seen_1175": "Clause 'implements' dé<PERSON><PERSON> rencont<PERSON>e.", "implements_clauses_can_only_be_used_in_TypeScript_files_8005": "Les clauses 'implements' peuvent uniquement être utilisées dans les fichiers TypeScript.", "import_can_only_be_used_in_TypeScript_files_8002": "'import ... =' peut uniquement être utilisé dans les fichiers TypeScript.", "infer_declarations_are_only_permitted_in_the_extends_clause_of_a_conditional_type_1338": "Les déclarations 'infer' sont uniquement autorisées dans la clause 'extends' d’un type conditionnel.", "let_declarations_can_only_be_declared_inside_a_block_1157": "Les déclarations 'let' ne peuvent être déclarées que dans un bloc.", "let_is_not_allowed_to_be_used_as_a_name_in_let_or_const_declarations_2480": "'let' ne peut pas être utilisé comme nom dans les déclarations 'let' ou 'const'.", "module_AMD_or_UMD_or_System_or_ES6_then_Classic_Otherwise_Node_69010": "module === « AMD » ou « UMD » ou « System » ou « ES6 », puis « Classic », sinon « Node »", "module_system_or_esModuleInterop_6904": "module === « system » ou esModuleInterop", "new_expression_whose_target_lacks_a_construct_signature_implicitly_has_an_any_type_7009": "L'expression 'new', dont la cible ne dispose pas d'une signature de construction, possède implicitement un type 'any'.", "node_modules_bower_components_jspm_packages_plus_the_value_of_outDir_if_one_is_specified_6907": "« [ « node_modules », « bower_components », « jspm_packages »] », plus la valeur de « outDir » si elle est spécifiée.", "one_of_Colon_6900": "L'un de :", "one_or_more_Colon_6901": "un ou plusieurs :", "options_6024": "options", "or_JSX_element_expected_1145": "'{' ou élément JSX attendu.", "or_expected_1144": "'{' ou ';' attendu.", "package_json_does_not_have_a_0_field_6100": "'package.json' n'a aucun champ '{0}'.", "package_json_does_not_have_a_typesVersions_entry_that_matches_version_0_6207": "'package.json' n'a aucune entrée 'typesVersions' qui correspond à la version '{0}'.", "package_json_had_a_falsy_0_field_6220": "'package.json' a un champ '{0}' erroné.", "package_json_has_0_field_1_that_references_2_6101": "'package.json' a un champ '{0}' '{1}' qui fait référence à '{2}'.", "package_json_has_a_typesVersions_entry_0_that_is_not_a_valid_semver_range_6209": "'package.json' a une entrée 'typesVersions' '{0}' qui n'est pas une plage SemVer valide.", "package_json_has_a_typesVersions_entry_0_that_matches_compiler_version_1_looking_for_a_pattern_to_ma_6208": "'package.json' a une entrée 'typesVersions' '{0}' qui correspond à la version de compilateur '{1}'. Recherche d'un modèle correspondant au nom de module '{2}'.", "package_json_has_a_typesVersions_field_with_version_specific_path_mappings_6206": "'package.json' a un champ 'typesVersions' avec des mappages de chemins spécifiques à la version.", "package_json_scope_0_explicitly_maps_specifier_1_to_null_6274": "L’étendue package.json « {0} » mappe explicitement le spécificateur « {1} » sur la valeur null.", "package_json_scope_0_has_invalid_type_for_target_of_specifier_1_6275": "L’étendue package.json « {0} » a un type non valide pour la cible du spécificateur « {1} »", "package_json_scope_0_has_no_imports_defined_6273": "L’étendue package.json « {0} » ne comporte aucune importation définie.", "paths_option_is_specified_looking_for_a_pattern_to_match_module_name_0_6091": "L'option 'paths' est spécifiée. Recherche d'un modèle correspondant au nom de module '{0}'.", "readonly_modifier_can_only_appear_on_a_property_declaration_or_index_signature_1024": "Le modificateur 'readonly' peut apparaître uniquement dans une déclaration de propriété ou une signature d'index.", "readonly_type_modifier_is_only_permitted_on_array_and_tuple_literal_types_1354": "Le modificateur de type 'readonly' est uniquement autorisé sur les types littéraux de tableau et de tuple.", "require_call_may_be_converted_to_an_import_80005": "L'appel de 'require' peut être converti en import.", "resolution_mode_assertions_are_only_supported_when_moduleResolution_is_node16_or_nodenext_1452": "Les assertions 'resolution-mode' sont prises en charge uniquement quand 'moduleResolution' est 'node16' ou 'nodenext'.", "resolution_mode_assertions_are_unstable_Use_nightly_TypeScript_to_silence_this_error_Try_updating_wi_4125": "Les assertions 'resolution-mode' sont instables. Utilisez TypeScript nocturne pour désactiver cette erreur. Essayez de mettre à jour avec « npm install -D typescript@next ».", "resolution_mode_can_only_be_set_for_type_only_imports_1454": "« mode résolution » ne peut être défini que pour les importations de type uniquement.", "resolution_mode_is_the_only_valid_key_for_type_import_assertions_1455": "« mode résolution » est la seule clé valide pour les assertions d’importation de type.", "resolution_mode_should_be_either_require_or_import_1453": "'resolution-mode' doit être 'require' ou 'import'.", "rootDirs_option_is_set_using_it_to_resolve_relative_module_name_0_6107": "L'option 'rootDirs' est définie. Utilisation de celle-ci pour la résolution du nom de module relatif '{0}'.", "super_can_only_be_referenced_in_a_derived_class_2335": "'super' ne peut être référencé que dans une classe dérivée.", "super_can_only_be_referenced_in_members_of_derived_classes_or_object_literal_expressions_2660": "'super' ne peut être référencé que dans les membres des classes dérivées ou les expressions littérales d'objet.", "super_cannot_be_referenced_in_a_computed_property_name_2466": "Impossible de référencer 'super' dans un nom de propriété calculée.", "super_cannot_be_referenced_in_constructor_arguments_2336": "Impossible de référencer 'super' dans des arguments de constructeur.", "super_is_only_allowed_in_members_of_object_literal_expressions_when_option_target_is_ES2015_or_highe_2659": "'super' est uniquement autorisé dans les membres des expressions littérales d'objet quand l'option 'target' a la valeur 'ES2015' ou une valeur supérieure.", "super_may_not_use_type_arguments_2754": "'super' ne peut pas utiliser d'arguments de type.", "super_must_be_called_before_accessing_a_property_of_super_in_the_constructor_of_a_derived_class_17011": "'super' doit être appelé avant d'accéder à une propriété de 'super' dans le constructeur d'une classe dérivée.", "super_must_be_called_before_accessing_this_in_the_constructor_of_a_derived_class_17009": "'super' doit être appelé avant d'accéder à 'this' dans le constructeur d'une classe dérivée.", "super_must_be_followed_by_an_argument_list_or_member_access_1034": "'super' doit être suivi d'une liste d'arguments ou d'un accès au membre.", "super_property_access_is_permitted_only_in_a_constructor_member_function_or_member_accessor_of_a_der_2338": "L'accès aux propriétés 'super' est autorisé uniquement dans un constructeur, une fonction membre ou un accesseur membre d'une classe dérivée.", "this_cannot_be_referenced_in_a_computed_property_name_2465": "Impossible de référencer 'this' dans un nom de propriété calculée.", "this_cannot_be_referenced_in_a_module_or_namespace_body_2331": "Impossible de référencer 'this' dans le corps d'un module ou d'un espace de noms.", "this_cannot_be_referenced_in_a_static_property_initializer_2334": "Impossible de référencer 'this' dans un initialiseur de propriété statique.", "this_cannot_be_referenced_in_constructor_arguments_2333": "Impossible de référencer 'this' dans des arguments de constructeur.", "this_cannot_be_referenced_in_current_location_2332": "Impossible de référencer 'this' dans l'emplacement actuel.", "this_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_2683": "'this' possède implicitement le type 'any', car il n'a pas d'annotation de type.", "true_for_ES2022_and_above_including_ESNext_6930": "'true’ pour ES2022 et versions ultérieures, y compris ESNext.", "true_if_composite_false_otherwise_6909": "« true » si « composite », « false » sinon", "tsc_Colon_The_TypeScript_Compiler_6922": "tsc : compilateur TypeScript", "type_Colon_6902": "type :", "unique_symbol_types_are_not_allowed_here_1335": "Les types 'unique symbol' ne sont pas autorisés ici.", "unique_symbol_types_are_only_allowed_on_variables_in_a_variable_statement_1334": "Les types 'unique symbol' sont uniquement autorisés sur les variables d'une déclaration de variable.", "unique_symbol_types_may_not_be_used_on_a_variable_declaration_with_a_binding_name_1333": "Les types 'unique symbol' ne peuvent pas être utilisés dans une déclaration de variable avec un nom de liaison.", "use_strict_directive_cannot_be_used_with_non_simple_parameter_list_1347": "La directive 'use strict' ne peut pas être utilisée avec une liste de paramètres non simple.", "use_strict_directive_used_here_1349": "directive 'use strict' utilisée ici.", "with_statements_are_not_allowed_in_an_async_function_block_1300": "Les instructions 'with' ne sont pas autorisées dans un bloc de fonctions async.", "with_statements_are_not_allowed_in_strict_mode_1101": "Les instructions 'with' ne sont pas autorisées en mode strict.", "yield_expression_implicitly_results_in_an_any_type_because_its_containing_generator_lacks_a_return_t_7057": "L'expression 'yield' génère implicitement un type 'any', car le générateur qui la contient n'a pas d'annotation de type de retour.", "yield_expressions_cannot_be_used_in_a_parameter_initializer_2523": "Impossible d'utiliser des expressions 'yield' dans un initialiseur de paramètre."}