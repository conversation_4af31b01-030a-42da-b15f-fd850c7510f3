[package]
name = "rustlog"
version = "0.1.0"
edition = "2021"

[dependencies]
aide = { version = "0.14.0", features = [
    "axum",
    "axum-json",
    "axum-query",
    "redoc",
] }
anyhow = "1.0.75"
axum = { version = "0.8.1", features = ["tokio"] }
chrono = { version = "0.4.27", features = ["serde"] }
clap = { version = "4.4.1", features = ["derive"] }
clickhouse = { version = "0.13.0", default-features = false, features = [
    "lz4",
    "uuid",
    "inserter",
] }
uuid = { version = "1.8.0", features = ["v4", "serde"] }
dashmap = { version = "6.1.0", features = ["serde"] }
flate2 = "1.0.27"
futures = "0.3.28"
indexmap = "2.2.6"
lazy_static = "1.4.0"
mimalloc = { version = "0.1.38", default-features = false }
mime_guess = "2.0.4"
prometheus = "0.13.3"
rand = "0.9.0"
rayon = "1.7.0"
reqwest = { version = "0.12.4", features = [
    "rustls-tls",
], default-features = false }
rust-embed = { version = "8.0.0", features = ["interpolate-folder-path"] }
schemars = "0.8.13"
serde = { version = "1.0.188", features = ["derive"] }
serde_json = { version = "1.0.105", features = ["preserve_order"] }
serde_repr = "0.1.16"
strum = { version = "0.26.2", features = ["derive"] }
thiserror = "1.0.47"
tokio = { version = "1.32.0", features = ["sync", "signal", "rt-multi-thread"] }
tower-http = { version = "0.6.1", features = [
    "trace",
    "cors",
    "normalize-path",
    "compression-full",
] }
tracing = "0.1.37"
tracing-subscriber = { version = "0.3.17", features = ["env-filter"] }
twitch-irc = { version = "5.0.1", default-features = false, features = [
    "metrics-collection",
    "transport-tcp-rustls-webpki-roots",
] }
twitch_api = { version = "0.7.0", features = [
    "client",
    "helix",
    "reqwest",
    "twitch_oauth2",
] }
tmi = { version = "0.7.0", default-features = false, features = ["simd"] }
axum-prometheus = "0.8.0"
metrics-prometheus = "0.8.0"
axum-extra = { version = "0.10.0", features = ["typed-header"] }
bitflags = { version = "2.5.0", features = ["serde"] }

[dev-dependencies]
pretty_assertions = "1.4.0"

[profile.release]
strip = true
lto = "thin"
