export interface MenuChangeNotifiers {
    /**
     * Calls all the registered highlight change handlers.
     *
     * @param newValue - The newly highlighted value.
     */
    notifyHighlightChanged: (newValue: string | null) => void;
    registerHighlightChangeHandler: (handler: (newValue: string | null) => void) => () => void;
}
/**
 * @ignore - internal hook.
 *
 * This hook is used to notify any interested components about changes in the Menu's selection and highlight.
 */
export default function useMenuChangeNotifiers(): MenuChangeNotifiers;
