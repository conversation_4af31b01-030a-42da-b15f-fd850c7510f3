export default {
  "code[class*=language-]": {
    "color": "#3c3836",
    "fontFamily": "Consolas,Monaco,\"Andale Mono\",monospace",
    "direction": "ltr",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "lineHeight": "1.5",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none"
  },
  "pre[class*=language-]": {
    "color": "#3c3836",
    "fontFamily": "Consolas,Monaco,\"Andale Mono\",monospace",
    "direction": "ltr",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "lineHeight": "1.5",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "padding": "1em",
    "margin": ".5em 0",
    "overflow": "auto",
    "background": "#f9f5d7"
  },
  "code[class*=language-] ::-moz-selection": {
    "color": "#282828",
    "background": "#a89984"
  },
  "code[class*=language-]::-moz-selection": {
    "color": "#282828",
    "background": "#a89984"
  },
  "pre[class*=language-] ::-moz-selection": {
    "color": "#282828",
    "background": "#a89984"
  },
  "pre[class*=language-]::-moz-selection": {
    "color": "#282828",
    "background": "#a89984"
  },
  "code[class*=language-] ::selection": {
    "color": "#282828",
    "background": "#a89984"
  },
  "code[class*=language-]::selection": {
    "color": "#282828",
    "background": "#a89984"
  },
  "pre[class*=language-] ::selection": {
    "color": "#282828",
    "background": "#a89984"
  },
  "pre[class*=language-]::selection": {
    "color": "#282828",
    "background": "#a89984"
  },
  ":not(pre)>code[class*=language-]": {
    "background": "#f9f5d7",
    "padding": ".1em",
    "borderRadius": ".3em"
  },
  "cdata": {
    "color": "#7c6f64"
  },
  "comment": {
    "color": "#7c6f64"
  },
  "prolog": {
    "color": "#7c6f64"
  },
  "atrule": {
    "color": "#9d0006"
  },
  "boolean": {
    "color": "#9d0006"
  },
  "delimiter": {
    "color": "#9d0006"
  },
  "important": {
    "color": "#9d0006"
  },
  "keyword": {
    "color": "#9d0006"
  },
  "selector": {
    "color": "#9d0006"
  },
  "attr-name": {
    "color": "#7c6f64"
  },
  "operator": {
    "color": "#7c6f64"
  },
  "punctuation": {
    "color": "#7c6f64"
  },
  "builtin": {
    "color": "#b57614"
  },
  "doctype": {
    "color": "#b57614"
  },
  "tag": {
    "color": "#b57614"
  },
  "tag.punctuation": {
    "color": "#b57614"
  },
  "entity": {
    "color": "#8f3f71"
  },
  "number": {
    "color": "#8f3f71"
  },
  "symbol": {
    "color": "#8f3f71"
  },
  "constant": {
    "color": "#9d0006"
  },
  "property": {
    "color": "#9d0006"
  },
  "variable": {
    "color": "#9d0006"
  },
  "char": {
    "color": "#797403"
  },
  "string": {
    "color": "#797403"
  },
  "attr-value": {
    "color": "#7c6f64"
  },
  "attr-value.punctuation": {
    "color": "#7c6f64"
  },
  "url": {
    "color": "#797403",
    "textDecoration": "underline"
  },
  "function": {
    "color": "#b57614"
  },
  "regex": {
    "background": "#797403"
  },
  "bold": {
    "fontWeight": "700"
  },
  "italic": {
    "fontStyle": "italic"
  },
  "inserted": {
    "background": "#7c6f64"
  },
  "deleted": {
    "background": "#9d0006"
  }
};