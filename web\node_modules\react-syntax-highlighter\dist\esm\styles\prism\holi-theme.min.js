export default {
  "code[class*=language-]": {
    "color": "#d6e7ff",
    "background": "#030314",
    "textShadow": "none",
    "fontFamily": "Consolas,Monaco,\"Andale Mono\",\"Ubuntu Mono\",monospace",
    "fontSize": "1em",
    "lineHeight": "1.5",
    "letterSpacing": ".2px",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "wordWrap": "normal",
    "textAlign": "left",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none"
  },
  "pre[class*=language-]": {
    "color": "#d6e7ff",
    "background": "#030314",
    "textShadow": "none",
    "fontFamily": "<PERSON>solas,Monaco,\"Andale Mono\",\"Ubuntu Mono\",monospace",
    "fontSize": "1em",
    "lineHeight": "1.5",
    "letterSpacing": ".2px",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "wordWrap": "normal",
    "textAlign": "left",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "border": "1px solid #2a4555",
    "borderRadius": "5px",
    "padding": "1.5em 1em",
    "margin": "1em 0",
    "overflow": "auto"
  },
  "code[class*=language-] ::-moz-selection": {
    "color": "inherit",
    "background": "#1d3b54",
    "textShadow": "none"
  },
  "code[class*=language-] ::selection": {
    "color": "inherit",
    "background": "#1d3b54",
    "textShadow": "none"
  },
  "code[class*=language-]::-moz-selection": {
    "color": "inherit",
    "background": "#1d3b54",
    "textShadow": "none"
  },
  "code[class*=language-]::selection": {
    "color": "inherit",
    "background": "#1d3b54",
    "textShadow": "none"
  },
  "pre[class*=language-] ::-moz-selection": {
    "color": "inherit",
    "background": "#1d3b54",
    "textShadow": "none"
  },
  "pre[class*=language-] ::selection": {
    "color": "inherit",
    "background": "#1d3b54",
    "textShadow": "none"
  },
  "pre[class*=language-]::-moz-selection": {
    "color": "inherit",
    "background": "#1d3b54",
    "textShadow": "none"
  },
  "pre[class*=language-]::selection": {
    "color": "inherit",
    "background": "#1d3b54",
    "textShadow": "none"
  },
  ":not(pre)>code[class*=language-]": {
    "color": "#f0f6f6",
    "background": "#2a4555",
    "padding": ".2em .3em",
    "borderRadius": ".2em",
    "boxDecorationBreak": "clone"
  },
  "cdata": {
    "color": "#446e69"
  },
  "comment": {
    "color": "#446e69"
  },
  "doctype": {
    "color": "#446e69"
  },
  "prolog": {
    "color": "#446e69"
  },
  "punctuation": {
    "color": "#d6b007"
  },
  "boolean": {
    "color": "#d6e7ff"
  },
  "constant": {
    "color": "#d6e7ff"
  },
  "deleted": {
    "color": "#d6e7ff"
  },
  "number": {
    "color": "#d6e7ff"
  },
  "property": {
    "color": "#d6e7ff"
  },
  "symbol": {
    "color": "#d6e7ff"
  },
  "tag": {
    "color": "#d6e7ff"
  },
  "attr-name": {
    "color": "#e60067"
  },
  "builtin": {
    "color": "#e60067"
  },
  "inserted": {
    "color": "#e60067"
  },
  "selector": {
    "color": "#e60067"
  },
  "char": {
    "color": "#49c6ec"
  },
  "string": {
    "color": "#49c6ec"
  },
  ".language-css .token.string": {
    "color": "#ec8e01",
    "background": "0 0"
  },
  ".style .token.string": {
    "color": "#ec8e01",
    "background": "0 0"
  },
  "entity": {
    "color": "#ec8e01",
    "background": "0 0"
  },
  "operator": {
    "color": "#ec8e01",
    "background": "0 0"
  },
  "url": {
    "color": "#ec8e01",
    "background": "0 0"
  },
  "atrule": {
    "color": "#0fe468"
  },
  "attr-value": {
    "color": "#0fe468"
  },
  "keyword": {
    "color": "#0fe468"
  },
  "class-name": {
    "color": "#78f3e9"
  },
  "function": {
    "color": "#78f3e9"
  },
  "important": {
    "color": "#d6e7ff"
  },
  "regex": {
    "color": "#d6e7ff"
  },
  "variable": {
    "color": "#d6e7ff"
  }
};