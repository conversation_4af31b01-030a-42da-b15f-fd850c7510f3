export { default as unstable_ClassNameGenerator } from '@mui/utils/ClassNameGenerator';
export { default as capitalize } from "./capitalize.js";
export { default as createChainedFunction } from "./createChainedFunction.js";
export { default as createSvgIcon } from "./createSvgIcon.js";
export { default as debounce } from "./debounce.js";
export { default as deprecatedPropType } from "./deprecatedPropType.js";
export { default as isMuiElement } from "./isMuiElement.js";
export { default as unstable_memoTheme } from "./memoTheme.js";
export { default as ownerDocument } from "./ownerDocument.js";
export { default as ownerWindow } from "./ownerWindow.js";
export { default as requirePropFactory } from "./requirePropFactory.js";
export { default as setRef } from "./setRef.js";
export { default as unstable_useEnhancedEffect } from "./useEnhancedEffect.js";
export { default as unstable_useId } from "./useId.js";
export { default as unsupportedProp } from "./unsupportedProp.js";
export { default as useControlled } from "./useControlled.js";
export { default as useEventCallback } from "./useEventCallback.js";
export { default as useForkRef } from "./useForkRef.js";
export { default as mergeSlotProps } from "./mergeSlotProps.js";
export * from "./types.js";