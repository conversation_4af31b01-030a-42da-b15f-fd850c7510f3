export default {
  "code[class*=language-]": {
    "color": "#393a34",
    "fontFamily": "<PERSON>sol<PERSON>,\"Bitstream Vera Sans Mono\",\"Courier New\",Courier,monospace",
    "direction": "ltr",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "fontSize": ".9em",
    "lineHeight": "1.2em",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none"
  },
  "pre[class*=language-]": {
    "color": "#393a34",
    "fontFamily": "Consolas,\"Bitstream Vera Sans Mono\",\"Courier New\",Courier,monospace",
    "direction": "ltr",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "fontSize": ".9em",
    "lineHeight": "1.2em",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "padding": "1em",
    "margin": ".5em 0",
    "overflow": "auto",
    "border": "1px solid #ddd",
    "backgroundColor": "#fff"
  },
  "pre>code[class*=language-]": {
    "fontSize": "1em"
  },
  "code[class*=language-] ::-moz-selection": {
    "background": "#b3d4fc"
  },
  "code[class*=language-]::-moz-selection": {
    "background": "#b3d4fc"
  },
  "pre[class*=language-] ::-moz-selection": {
    "background": "#b3d4fc"
  },
  "pre[class*=language-]::-moz-selection": {
    "background": "#b3d4fc"
  },
  "code[class*=language-] ::selection": {
    "background": "#b3d4fc"
  },
  "code[class*=language-]::selection": {
    "background": "#b3d4fc"
  },
  "pre[class*=language-] ::selection": {
    "background": "#b3d4fc"
  },
  "pre[class*=language-]::selection": {
    "background": "#b3d4fc"
  },
  ":not(pre)>code[class*=language-]": {
    "padding": ".2em",
    "paddingTop": "1px",
    "paddingBottom": "1px",
    "background": "#f8f8f8",
    "border": "1px solid #ddd"
  },
  "cdata": {
    "color": "#998",
    "fontStyle": "italic"
  },
  "comment": {
    "color": "#998",
    "fontStyle": "italic"
  },
  "doctype": {
    "color": "#998",
    "fontStyle": "italic"
  },
  "prolog": {
    "color": "#998",
    "fontStyle": "italic"
  },
  "namespace": {
    "Opacity": ".7"
  },
  "attr-value": {
    "color": "#e3116c"
  },
  "string": {
    "color": "#e3116c"
  },
  "operator": {
    "color": "#393a34"
  },
  "punctuation": {
    "color": "#393a34"
  },
  "boolean": {
    "color": "#36acaa"
  },
  "constant": {
    "color": "#36acaa"
  },
  "entity": {
    "color": "#36acaa"
  },
  "inserted": {
    "color": "#36acaa"
  },
  "number": {
    "color": "#36acaa"
  },
  "property": {
    "color": "#36acaa"
  },
  "regex": {
    "color": "#36acaa"
  },
  "symbol": {
    "color": "#36acaa"
  },
  "url": {
    "color": "#36acaa"
  },
  "variable": {
    "color": "#36acaa"
  },
  ".language-autohotkey .token.selector": {
    "color": "#00a4db"
  },
  "atrule": {
    "color": "#00a4db"
  },
  "attr-name": {
    "color": "#00a4db"
  },
  "keyword": {
    "color": "#00a4db"
  },
  ".language-autohotkey .token.tag": {
    "color": "#9a050f"
  },
  "deleted": {
    "color": "#9a050f"
  },
  "function": {
    "color": "#9a050f",
    "fontWeight": "700"
  },
  ".language-autohotkey .token.keyword": {
    "color": "#00009f"
  },
  "selector": {
    "color": "#00009f"
  },
  "tag": {
    "color": "#00009f"
  },
  "bold": {
    "fontWeight": "700"
  },
  "important": {
    "fontWeight": "700"
  },
  "italic": {
    "fontStyle": "italic"
  }
};