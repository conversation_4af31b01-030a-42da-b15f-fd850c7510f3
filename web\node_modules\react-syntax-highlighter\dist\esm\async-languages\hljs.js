import createLanguageAsyncLoader from "./create-language-async-loader";
export default {
  oneC: createLanguageAsyncLoader("oneC", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_oneC" */
    "highlight.js/lib/languages/1c");
  }),
  abnf: createLanguageAsyncLoader("abnf", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_abnf" */
    "highlight.js/lib/languages/abnf");
  }),
  accesslog: createLanguageAsyncLoader("accesslog", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_accesslog" */
    "highlight.js/lib/languages/accesslog");
  }),
  actionscript: createLanguageAsyncLoader("actionscript", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_actionscript" */
    "highlight.js/lib/languages/actionscript");
  }),
  ada: createLanguageAsyncLoader("ada", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_ada" */
    "highlight.js/lib/languages/ada");
  }),
  angelscript: createLanguageAsyncLoader("angelscript", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_angelscript" */
    "highlight.js/lib/languages/angelscript");
  }),
  apache: createLanguageAsyncLoader("apache", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_apache" */
    "highlight.js/lib/languages/apache");
  }),
  applescript: createLanguageAsyncLoader("applescript", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_applescript" */
    "highlight.js/lib/languages/applescript");
  }),
  arcade: createLanguageAsyncLoader("arcade", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_arcade" */
    "highlight.js/lib/languages/arcade");
  }),
  arduino: createLanguageAsyncLoader("arduino", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_arduino" */
    "highlight.js/lib/languages/arduino");
  }),
  armasm: createLanguageAsyncLoader("armasm", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_armasm" */
    "highlight.js/lib/languages/armasm");
  }),
  asciidoc: createLanguageAsyncLoader("asciidoc", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_asciidoc" */
    "highlight.js/lib/languages/asciidoc");
  }),
  aspectj: createLanguageAsyncLoader("aspectj", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_aspectj" */
    "highlight.js/lib/languages/aspectj");
  }),
  autohotkey: createLanguageAsyncLoader("autohotkey", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_autohotkey" */
    "highlight.js/lib/languages/autohotkey");
  }),
  autoit: createLanguageAsyncLoader("autoit", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_autoit" */
    "highlight.js/lib/languages/autoit");
  }),
  avrasm: createLanguageAsyncLoader("avrasm", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_avrasm" */
    "highlight.js/lib/languages/avrasm");
  }),
  awk: createLanguageAsyncLoader("awk", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_awk" */
    "highlight.js/lib/languages/awk");
  }),
  axapta: createLanguageAsyncLoader("axapta", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_axapta" */
    "highlight.js/lib/languages/axapta");
  }),
  bash: createLanguageAsyncLoader("bash", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_bash" */
    "highlight.js/lib/languages/bash");
  }),
  basic: createLanguageAsyncLoader("basic", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_basic" */
    "highlight.js/lib/languages/basic");
  }),
  bnf: createLanguageAsyncLoader("bnf", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_bnf" */
    "highlight.js/lib/languages/bnf");
  }),
  brainfuck: createLanguageAsyncLoader("brainfuck", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_brainfuck" */
    "highlight.js/lib/languages/brainfuck");
  }),
  cLike: createLanguageAsyncLoader("cLike", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_cLike" */
    "highlight.js/lib/languages/c-like");
  }),
  c: createLanguageAsyncLoader("c", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_c" */
    "highlight.js/lib/languages/c");
  }),
  cal: createLanguageAsyncLoader("cal", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_cal" */
    "highlight.js/lib/languages/cal");
  }),
  capnproto: createLanguageAsyncLoader("capnproto", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_capnproto" */
    "highlight.js/lib/languages/capnproto");
  }),
  ceylon: createLanguageAsyncLoader("ceylon", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_ceylon" */
    "highlight.js/lib/languages/ceylon");
  }),
  clean: createLanguageAsyncLoader("clean", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_clean" */
    "highlight.js/lib/languages/clean");
  }),
  clojureRepl: createLanguageAsyncLoader("clojureRepl", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_clojureRepl" */
    "highlight.js/lib/languages/clojure-repl");
  }),
  clojure: createLanguageAsyncLoader("clojure", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_clojure" */
    "highlight.js/lib/languages/clojure");
  }),
  cmake: createLanguageAsyncLoader("cmake", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_cmake" */
    "highlight.js/lib/languages/cmake");
  }),
  coffeescript: createLanguageAsyncLoader("coffeescript", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_coffeescript" */
    "highlight.js/lib/languages/coffeescript");
  }),
  coq: createLanguageAsyncLoader("coq", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_coq" */
    "highlight.js/lib/languages/coq");
  }),
  cos: createLanguageAsyncLoader("cos", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_cos" */
    "highlight.js/lib/languages/cos");
  }),
  cpp: createLanguageAsyncLoader("cpp", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_cpp" */
    "highlight.js/lib/languages/cpp");
  }),
  crmsh: createLanguageAsyncLoader("crmsh", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_crmsh" */
    "highlight.js/lib/languages/crmsh");
  }),
  crystal: createLanguageAsyncLoader("crystal", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_crystal" */
    "highlight.js/lib/languages/crystal");
  }),
  csharp: createLanguageAsyncLoader("csharp", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_csharp" */
    "highlight.js/lib/languages/csharp");
  }),
  csp: createLanguageAsyncLoader("csp", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_csp" */
    "highlight.js/lib/languages/csp");
  }),
  css: createLanguageAsyncLoader("css", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_css" */
    "highlight.js/lib/languages/css");
  }),
  d: createLanguageAsyncLoader("d", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_d" */
    "highlight.js/lib/languages/d");
  }),
  dart: createLanguageAsyncLoader("dart", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_dart" */
    "highlight.js/lib/languages/dart");
  }),
  delphi: createLanguageAsyncLoader("delphi", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_delphi" */
    "highlight.js/lib/languages/delphi");
  }),
  diff: createLanguageAsyncLoader("diff", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_diff" */
    "highlight.js/lib/languages/diff");
  }),
  django: createLanguageAsyncLoader("django", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_django" */
    "highlight.js/lib/languages/django");
  }),
  dns: createLanguageAsyncLoader("dns", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_dns" */
    "highlight.js/lib/languages/dns");
  }),
  dockerfile: createLanguageAsyncLoader("dockerfile", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_dockerfile" */
    "highlight.js/lib/languages/dockerfile");
  }),
  dos: createLanguageAsyncLoader("dos", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_dos" */
    "highlight.js/lib/languages/dos");
  }),
  dsconfig: createLanguageAsyncLoader("dsconfig", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_dsconfig" */
    "highlight.js/lib/languages/dsconfig");
  }),
  dts: createLanguageAsyncLoader("dts", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_dts" */
    "highlight.js/lib/languages/dts");
  }),
  dust: createLanguageAsyncLoader("dust", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_dust" */
    "highlight.js/lib/languages/dust");
  }),
  ebnf: createLanguageAsyncLoader("ebnf", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_ebnf" */
    "highlight.js/lib/languages/ebnf");
  }),
  elixir: createLanguageAsyncLoader("elixir", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_elixir" */
    "highlight.js/lib/languages/elixir");
  }),
  elm: createLanguageAsyncLoader("elm", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_elm" */
    "highlight.js/lib/languages/elm");
  }),
  erb: createLanguageAsyncLoader("erb", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_erb" */
    "highlight.js/lib/languages/erb");
  }),
  erlangRepl: createLanguageAsyncLoader("erlangRepl", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_erlangRepl" */
    "highlight.js/lib/languages/erlang-repl");
  }),
  erlang: createLanguageAsyncLoader("erlang", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_erlang" */
    "highlight.js/lib/languages/erlang");
  }),
  excel: createLanguageAsyncLoader("excel", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_excel" */
    "highlight.js/lib/languages/excel");
  }),
  fix: createLanguageAsyncLoader("fix", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_fix" */
    "highlight.js/lib/languages/fix");
  }),
  flix: createLanguageAsyncLoader("flix", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_flix" */
    "highlight.js/lib/languages/flix");
  }),
  fortran: createLanguageAsyncLoader("fortran", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_fortran" */
    "highlight.js/lib/languages/fortran");
  }),
  fsharp: createLanguageAsyncLoader("fsharp", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_fsharp" */
    "highlight.js/lib/languages/fsharp");
  }),
  gams: createLanguageAsyncLoader("gams", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_gams" */
    "highlight.js/lib/languages/gams");
  }),
  gauss: createLanguageAsyncLoader("gauss", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_gauss" */
    "highlight.js/lib/languages/gauss");
  }),
  gcode: createLanguageAsyncLoader("gcode", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_gcode" */
    "highlight.js/lib/languages/gcode");
  }),
  gherkin: createLanguageAsyncLoader("gherkin", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_gherkin" */
    "highlight.js/lib/languages/gherkin");
  }),
  glsl: createLanguageAsyncLoader("glsl", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_glsl" */
    "highlight.js/lib/languages/glsl");
  }),
  gml: createLanguageAsyncLoader("gml", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_gml" */
    "highlight.js/lib/languages/gml");
  }),
  go: createLanguageAsyncLoader("go", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_go" */
    "highlight.js/lib/languages/go");
  }),
  golo: createLanguageAsyncLoader("golo", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_golo" */
    "highlight.js/lib/languages/golo");
  }),
  gradle: createLanguageAsyncLoader("gradle", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_gradle" */
    "highlight.js/lib/languages/gradle");
  }),
  groovy: createLanguageAsyncLoader("groovy", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_groovy" */
    "highlight.js/lib/languages/groovy");
  }),
  haml: createLanguageAsyncLoader("haml", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_haml" */
    "highlight.js/lib/languages/haml");
  }),
  handlebars: createLanguageAsyncLoader("handlebars", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_handlebars" */
    "highlight.js/lib/languages/handlebars");
  }),
  haskell: createLanguageAsyncLoader("haskell", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_haskell" */
    "highlight.js/lib/languages/haskell");
  }),
  haxe: createLanguageAsyncLoader("haxe", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_haxe" */
    "highlight.js/lib/languages/haxe");
  }),
  hsp: createLanguageAsyncLoader("hsp", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_hsp" */
    "highlight.js/lib/languages/hsp");
  }),
  htmlbars: createLanguageAsyncLoader("htmlbars", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_htmlbars" */
    "highlight.js/lib/languages/htmlbars");
  }),
  http: createLanguageAsyncLoader("http", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_http" */
    "highlight.js/lib/languages/http");
  }),
  hy: createLanguageAsyncLoader("hy", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_hy" */
    "highlight.js/lib/languages/hy");
  }),
  inform7: createLanguageAsyncLoader("inform7", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_inform7" */
    "highlight.js/lib/languages/inform7");
  }),
  ini: createLanguageAsyncLoader("ini", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_ini" */
    "highlight.js/lib/languages/ini");
  }),
  irpf90: createLanguageAsyncLoader("irpf90", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_irpf90" */
    "highlight.js/lib/languages/irpf90");
  }),
  isbl: createLanguageAsyncLoader("isbl", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_isbl" */
    "highlight.js/lib/languages/isbl");
  }),
  java: createLanguageAsyncLoader("java", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_java" */
    "highlight.js/lib/languages/java");
  }),
  javascript: createLanguageAsyncLoader("javascript", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_javascript" */
    "highlight.js/lib/languages/javascript");
  }),
  jbossCli: createLanguageAsyncLoader("jbossCli", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_jbossCli" */
    "highlight.js/lib/languages/jboss-cli");
  }),
  json: createLanguageAsyncLoader("json", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_json" */
    "highlight.js/lib/languages/json");
  }),
  juliaRepl: createLanguageAsyncLoader("juliaRepl", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_juliaRepl" */
    "highlight.js/lib/languages/julia-repl");
  }),
  julia: createLanguageAsyncLoader("julia", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_julia" */
    "highlight.js/lib/languages/julia");
  }),
  kotlin: createLanguageAsyncLoader("kotlin", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_kotlin" */
    "highlight.js/lib/languages/kotlin");
  }),
  lasso: createLanguageAsyncLoader("lasso", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_lasso" */
    "highlight.js/lib/languages/lasso");
  }),
  latex: createLanguageAsyncLoader("latex", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_latex" */
    "highlight.js/lib/languages/latex");
  }),
  ldif: createLanguageAsyncLoader("ldif", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_ldif" */
    "highlight.js/lib/languages/ldif");
  }),
  leaf: createLanguageAsyncLoader("leaf", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_leaf" */
    "highlight.js/lib/languages/leaf");
  }),
  less: createLanguageAsyncLoader("less", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_less" */
    "highlight.js/lib/languages/less");
  }),
  lisp: createLanguageAsyncLoader("lisp", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_lisp" */
    "highlight.js/lib/languages/lisp");
  }),
  livecodeserver: createLanguageAsyncLoader("livecodeserver", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_livecodeserver" */
    "highlight.js/lib/languages/livecodeserver");
  }),
  livescript: createLanguageAsyncLoader("livescript", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_livescript" */
    "highlight.js/lib/languages/livescript");
  }),
  llvm: createLanguageAsyncLoader("llvm", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_llvm" */
    "highlight.js/lib/languages/llvm");
  }),
  lsl: createLanguageAsyncLoader("lsl", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_lsl" */
    "highlight.js/lib/languages/lsl");
  }),
  lua: createLanguageAsyncLoader("lua", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_lua" */
    "highlight.js/lib/languages/lua");
  }),
  makefile: createLanguageAsyncLoader("makefile", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_makefile" */
    "highlight.js/lib/languages/makefile");
  }),
  markdown: createLanguageAsyncLoader("markdown", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_markdown" */
    "highlight.js/lib/languages/markdown");
  }),
  mathematica: createLanguageAsyncLoader("mathematica", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_mathematica" */
    "highlight.js/lib/languages/mathematica");
  }),
  matlab: createLanguageAsyncLoader("matlab", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_matlab" */
    "highlight.js/lib/languages/matlab");
  }),
  maxima: createLanguageAsyncLoader("maxima", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_maxima" */
    "highlight.js/lib/languages/maxima");
  }),
  mel: createLanguageAsyncLoader("mel", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_mel" */
    "highlight.js/lib/languages/mel");
  }),
  mercury: createLanguageAsyncLoader("mercury", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_mercury" */
    "highlight.js/lib/languages/mercury");
  }),
  mipsasm: createLanguageAsyncLoader("mipsasm", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_mipsasm" */
    "highlight.js/lib/languages/mipsasm");
  }),
  mizar: createLanguageAsyncLoader("mizar", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_mizar" */
    "highlight.js/lib/languages/mizar");
  }),
  mojolicious: createLanguageAsyncLoader("mojolicious", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_mojolicious" */
    "highlight.js/lib/languages/mojolicious");
  }),
  monkey: createLanguageAsyncLoader("monkey", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_monkey" */
    "highlight.js/lib/languages/monkey");
  }),
  moonscript: createLanguageAsyncLoader("moonscript", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_moonscript" */
    "highlight.js/lib/languages/moonscript");
  }),
  n1ql: createLanguageAsyncLoader("n1ql", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_n1ql" */
    "highlight.js/lib/languages/n1ql");
  }),
  nginx: createLanguageAsyncLoader("nginx", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_nginx" */
    "highlight.js/lib/languages/nginx");
  }),
  nim: createLanguageAsyncLoader("nim", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_nim" */
    "highlight.js/lib/languages/nim");
  }),
  nix: createLanguageAsyncLoader("nix", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_nix" */
    "highlight.js/lib/languages/nix");
  }),
  nodeRepl: createLanguageAsyncLoader("nodeRepl", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_nodeRepl" */
    "highlight.js/lib/languages/node-repl");
  }),
  nsis: createLanguageAsyncLoader("nsis", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_nsis" */
    "highlight.js/lib/languages/nsis");
  }),
  objectivec: createLanguageAsyncLoader("objectivec", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_objectivec" */
    "highlight.js/lib/languages/objectivec");
  }),
  ocaml: createLanguageAsyncLoader("ocaml", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_ocaml" */
    "highlight.js/lib/languages/ocaml");
  }),
  openscad: createLanguageAsyncLoader("openscad", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_openscad" */
    "highlight.js/lib/languages/openscad");
  }),
  oxygene: createLanguageAsyncLoader("oxygene", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_oxygene" */
    "highlight.js/lib/languages/oxygene");
  }),
  parser3: createLanguageAsyncLoader("parser3", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_parser3" */
    "highlight.js/lib/languages/parser3");
  }),
  perl: createLanguageAsyncLoader("perl", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_perl" */
    "highlight.js/lib/languages/perl");
  }),
  pf: createLanguageAsyncLoader("pf", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_pf" */
    "highlight.js/lib/languages/pf");
  }),
  pgsql: createLanguageAsyncLoader("pgsql", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_pgsql" */
    "highlight.js/lib/languages/pgsql");
  }),
  phpTemplate: createLanguageAsyncLoader("phpTemplate", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_phpTemplate" */
    "highlight.js/lib/languages/php-template");
  }),
  php: createLanguageAsyncLoader("php", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_php" */
    "highlight.js/lib/languages/php");
  }),
  plaintext: createLanguageAsyncLoader("plaintext", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_plaintext" */
    "highlight.js/lib/languages/plaintext");
  }),
  pony: createLanguageAsyncLoader("pony", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_pony" */
    "highlight.js/lib/languages/pony");
  }),
  powershell: createLanguageAsyncLoader("powershell", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_powershell" */
    "highlight.js/lib/languages/powershell");
  }),
  processing: createLanguageAsyncLoader("processing", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_processing" */
    "highlight.js/lib/languages/processing");
  }),
  profile: createLanguageAsyncLoader("profile", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_profile" */
    "highlight.js/lib/languages/profile");
  }),
  prolog: createLanguageAsyncLoader("prolog", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_prolog" */
    "highlight.js/lib/languages/prolog");
  }),
  properties: createLanguageAsyncLoader("properties", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_properties" */
    "highlight.js/lib/languages/properties");
  }),
  protobuf: createLanguageAsyncLoader("protobuf", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_protobuf" */
    "highlight.js/lib/languages/protobuf");
  }),
  puppet: createLanguageAsyncLoader("puppet", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_puppet" */
    "highlight.js/lib/languages/puppet");
  }),
  purebasic: createLanguageAsyncLoader("purebasic", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_purebasic" */
    "highlight.js/lib/languages/purebasic");
  }),
  pythonRepl: createLanguageAsyncLoader("pythonRepl", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_pythonRepl" */
    "highlight.js/lib/languages/python-repl");
  }),
  python: createLanguageAsyncLoader("python", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_python" */
    "highlight.js/lib/languages/python");
  }),
  q: createLanguageAsyncLoader("q", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_q" */
    "highlight.js/lib/languages/q");
  }),
  qml: createLanguageAsyncLoader("qml", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_qml" */
    "highlight.js/lib/languages/qml");
  }),
  r: createLanguageAsyncLoader("r", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_r" */
    "highlight.js/lib/languages/r");
  }),
  reasonml: createLanguageAsyncLoader("reasonml", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_reasonml" */
    "highlight.js/lib/languages/reasonml");
  }),
  rib: createLanguageAsyncLoader("rib", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_rib" */
    "highlight.js/lib/languages/rib");
  }),
  roboconf: createLanguageAsyncLoader("roboconf", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_roboconf" */
    "highlight.js/lib/languages/roboconf");
  }),
  routeros: createLanguageAsyncLoader("routeros", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_routeros" */
    "highlight.js/lib/languages/routeros");
  }),
  rsl: createLanguageAsyncLoader("rsl", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_rsl" */
    "highlight.js/lib/languages/rsl");
  }),
  ruby: createLanguageAsyncLoader("ruby", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_ruby" */
    "highlight.js/lib/languages/ruby");
  }),
  ruleslanguage: createLanguageAsyncLoader("ruleslanguage", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_ruleslanguage" */
    "highlight.js/lib/languages/ruleslanguage");
  }),
  rust: createLanguageAsyncLoader("rust", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_rust" */
    "highlight.js/lib/languages/rust");
  }),
  sas: createLanguageAsyncLoader("sas", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_sas" */
    "highlight.js/lib/languages/sas");
  }),
  scala: createLanguageAsyncLoader("scala", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_scala" */
    "highlight.js/lib/languages/scala");
  }),
  scheme: createLanguageAsyncLoader("scheme", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_scheme" */
    "highlight.js/lib/languages/scheme");
  }),
  scilab: createLanguageAsyncLoader("scilab", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_scilab" */
    "highlight.js/lib/languages/scilab");
  }),
  scss: createLanguageAsyncLoader("scss", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_scss" */
    "highlight.js/lib/languages/scss");
  }),
  shell: createLanguageAsyncLoader("shell", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_shell" */
    "highlight.js/lib/languages/shell");
  }),
  smali: createLanguageAsyncLoader("smali", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_smali" */
    "highlight.js/lib/languages/smali");
  }),
  smalltalk: createLanguageAsyncLoader("smalltalk", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_smalltalk" */
    "highlight.js/lib/languages/smalltalk");
  }),
  sml: createLanguageAsyncLoader("sml", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_sml" */
    "highlight.js/lib/languages/sml");
  }),
  sqf: createLanguageAsyncLoader("sqf", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_sqf" */
    "highlight.js/lib/languages/sqf");
  }),
  sql: createLanguageAsyncLoader("sql", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_sql" */
    "highlight.js/lib/languages/sql");
  }),
  sqlMore: createLanguageAsyncLoader("sqlMore", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_sqlMore" */
    "highlight.js/lib/languages/sql_more");
  }),
  stan: createLanguageAsyncLoader("stan", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_stan" */
    "highlight.js/lib/languages/stan");
  }),
  stata: createLanguageAsyncLoader("stata", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_stata" */
    "highlight.js/lib/languages/stata");
  }),
  step21: createLanguageAsyncLoader("step21", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_step21" */
    "highlight.js/lib/languages/step21");
  }),
  stylus: createLanguageAsyncLoader("stylus", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_stylus" */
    "highlight.js/lib/languages/stylus");
  }),
  subunit: createLanguageAsyncLoader("subunit", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_subunit" */
    "highlight.js/lib/languages/subunit");
  }),
  swift: createLanguageAsyncLoader("swift", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_swift" */
    "highlight.js/lib/languages/swift");
  }),
  taggerscript: createLanguageAsyncLoader("taggerscript", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_taggerscript" */
    "highlight.js/lib/languages/taggerscript");
  }),
  tap: createLanguageAsyncLoader("tap", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_tap" */
    "highlight.js/lib/languages/tap");
  }),
  tcl: createLanguageAsyncLoader("tcl", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_tcl" */
    "highlight.js/lib/languages/tcl");
  }),
  thrift: createLanguageAsyncLoader("thrift", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_thrift" */
    "highlight.js/lib/languages/thrift");
  }),
  tp: createLanguageAsyncLoader("tp", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_tp" */
    "highlight.js/lib/languages/tp");
  }),
  twig: createLanguageAsyncLoader("twig", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_twig" */
    "highlight.js/lib/languages/twig");
  }),
  typescript: createLanguageAsyncLoader("typescript", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_typescript" */
    "highlight.js/lib/languages/typescript");
  }),
  vala: createLanguageAsyncLoader("vala", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_vala" */
    "highlight.js/lib/languages/vala");
  }),
  vbnet: createLanguageAsyncLoader("vbnet", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_vbnet" */
    "highlight.js/lib/languages/vbnet");
  }),
  vbscriptHtml: createLanguageAsyncLoader("vbscriptHtml", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_vbscriptHtml" */
    "highlight.js/lib/languages/vbscript-html");
  }),
  vbscript: createLanguageAsyncLoader("vbscript", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_vbscript" */
    "highlight.js/lib/languages/vbscript");
  }),
  verilog: createLanguageAsyncLoader("verilog", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_verilog" */
    "highlight.js/lib/languages/verilog");
  }),
  vhdl: createLanguageAsyncLoader("vhdl", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_vhdl" */
    "highlight.js/lib/languages/vhdl");
  }),
  vim: createLanguageAsyncLoader("vim", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_vim" */
    "highlight.js/lib/languages/vim");
  }),
  x86asm: createLanguageAsyncLoader("x86asm", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_x86asm" */
    "highlight.js/lib/languages/x86asm");
  }),
  xl: createLanguageAsyncLoader("xl", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_xl" */
    "highlight.js/lib/languages/xl");
  }),
  xml: createLanguageAsyncLoader("xml", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_xml" */
    "highlight.js/lib/languages/xml");
  }),
  xquery: createLanguageAsyncLoader("xquery", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_xquery" */
    "highlight.js/lib/languages/xquery");
  }),
  yaml: createLanguageAsyncLoader("yaml", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_yaml" */
    "highlight.js/lib/languages/yaml");
  }),
  zephir: createLanguageAsyncLoader("zephir", function () {
    return import(
    /* webpackChunkName: "react-syntax-highlighter_languages_highlight_zephir" */
    "highlight.js/lib/languages/zephir");
  })
};