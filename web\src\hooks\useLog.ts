import { useContext } from "react";
import { useQuery } from "react-query";
import { getUserId, isUserId } from "../services/isUserId";
import { parseEmotes } from "../services/parseEmotes";
import { store } from "../store";
import { LogMessage, UserLogResponse } from "../types/log";

export function useLog(
  channel: string,
  username: string,
  year: string,
  month: string
): Array<LogMessage> {
  const { state } = useContext(store);

  const { data } = useQuery<Array<LogMessage>>(
    ["log", { channel: channel, username: username, year: year, month: month }],
    () => {
      if (channel && username) {
        const channelIsId = isUserId(channel);
        const usernameIsId = isUserId(username);

        if (channelIsId) {
          channel = getUserId(channel);
        }
        if (usernameIsId) {
          username = getUserId(username);
        }

        const queryUrl = new URL(
          `${state.apiBaseUrl}/channel${
            channelIsId ? "id" : ""
          }/${channel}/user${
            usernameIsId ? "id" : ""
          }/${username}/${year}/${month}`
        );
        queryUrl.searchParams.append("jsonBasic", "1");
        if (!state.settings.newOnBottom.value) {
          queryUrl.searchParams.append("reverse", "1");
        }

        return fetch(queryUrl.toString())
          .then((response) => {
            if (response.ok) {
              return response;
            }

            throw Error(response.statusText);
          })
          .then((response) => response.json())
          .then((data: UserLogResponse) => {
            const messages: Array<LogMessage> = [];

            for (const msg of data.messages) {
              messages.push({
                ...msg,
                timestamp: new Date(msg.timestamp),
                emotes: parseEmotes(msg.text, msg.tags["emotes"]),
              });
            }

            return messages;
          });
      }

      return [];
    },
    { refetchOnWindowFocus: false, refetchOnReconnect: false }
  );

  return data ?? [];
}
