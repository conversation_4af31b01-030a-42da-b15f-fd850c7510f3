import * as React from 'react';
import { MenuItemMetadata, MenuItemState } from '../useMenu';
export interface MenuUnstyledContextType {
    registerItem: (id: string, metadata: MenuItemMetadata) => void;
    unregisterItem: (id: string) => void;
    getItemState: (id: string) => MenuItemState | undefined;
    getItemProps: (id: string, otherHandlers?: Record<string, React.EventHandler<any>>) => Record<string, any>;
    open: boolean;
    registerHighlightChangeHandler: (handler: (itemId: string | null) => void) => void;
}
declare const MenuUnstyledContext: React.Context<MenuUnstyledContextType | null>;
export default MenuUnstyledContext;
