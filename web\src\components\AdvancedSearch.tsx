import React, { useState, useContext } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  FormControlLabel,
  Checkbox,
  Grid,
  Chip,
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
} from "@mui/material";
import { ExpandMore, Search, Clear } from "@mui/icons-material";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs, { Dayjs } from "dayjs";
import styled from "styled-components";
import { store } from "../store";

const StyledDialog = styled(Dialog)`
  .MuiDialog-paper {
    max-width: 800px;
    width: 90vw;
  }
`;

const FilterChip = styled(Chip)`
  margin: 2px;
`;

interface AdvancedSearchParams {
  q?: string;
  regex?: boolean;
  caseSensitive?: boolean;
  users?: string[];
  excludeUsers?: string[];
  messageTypes?: string[];
  badges?: string[];
  from?: Dayjs | null;
  to?: Dayjs | null;
  minLength?: number;
  maxLength?: number;
  firstMessagesOnly?: boolean;
  subscribersOnly?: boolean;
  vipsOnly?: boolean;
  moderatorsOnly?: boolean;
  hasEmotes?: boolean;
  searchDisplayNames?: boolean;
}

interface AdvancedSearchProps {
  open: boolean;
  onClose: () => void;
  onSearch: (params: AdvancedSearchParams) => void;
  initialParams?: AdvancedSearchParams;
}

const messageTypeOptions = [
  { value: "privmsg", label: "Chat Messages" },
  { value: "notice", label: "Notices" },
  { value: "clearchat", label: "Timeouts/Bans" },
  { value: "usernotice", label: "User Notices (Subs, etc.)" },
  { value: "whisper", label: "Whispers" },
];

const badgeOptions = [
  { value: "subscriber", label: "Subscribers" },
  { value: "vip", label: "VIPs" },
  { value: "moderator", label: "Moderators" },
  { value: "broadcaster", label: "Broadcaster" },
  { value: "staff", label: "Twitch Staff" },
  { value: "admin", label: "Twitch Admin" },
  { value: "global_mod", label: "Global Moderator" },
];

export function AdvancedSearch({
  open,
  onClose,
  onSearch,
  initialParams,
}: AdvancedSearchProps) {
  const { state } = useContext(store);
  const [params, setParams] = useState<AdvancedSearchParams>(
    initialParams || {}
  );

  const handleSearch = () => {
    onSearch(params);
    onClose();
  };

  const handleClear = () => {
    setParams({});
  };

  const updateParam = <K extends keyof AdvancedSearchParams>(
    key: K,
    value: AdvancedSearchParams[K]
  ) => {
    setParams((prev) => ({ ...prev, [key]: value }));
  };

  const addUser = (user: string) => {
    if (user.trim()) {
      const users = params.users || [];
      if (!users.includes(user.trim())) {
        updateParam("users", [...users, user.trim()]);
      }
    }
  };

  const removeUser = (user: string) => {
    const users = params.users || [];
    updateParam(
      "users",
      users.filter((u) => u !== user)
    );
  };

  const addExcludeUser = (user: string) => {
    if (user.trim()) {
      const excludeUsers = params.excludeUsers || [];
      if (!excludeUsers.includes(user.trim())) {
        updateParam("excludeUsers", [...excludeUsers, user.trim()]);
      }
    }
  };

  const removeExcludeUser = (user: string) => {
    const excludeUsers = params.excludeUsers || [];
    updateParam(
      "excludeUsers",
      excludeUsers.filter((u) => u !== user)
    );
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <StyledDialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={1}>
            <Search />
            Advanced Search
          </Box>
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={3}>
            {/* Basic Search */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Search Query"
                value={params.q || ""}
                onChange={(e) => updateParam("q", e.target.value)}
                placeholder="Enter search terms..."
                helperText="Use quotes for exact phrases, * for wildcards (if regex is enabled)"
              />
            </Grid>

            {/* Search Options */}
            <Grid item xs={12}>
              <Box display="flex" flexWrap="wrap" gap={2}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={params.regex || false}
                      onChange={(e) => updateParam("regex", e.target.checked)}
                    />
                  }
                  label="Regular Expression"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={params.caseSensitive || false}
                      onChange={(e) =>
                        updateParam("caseSensitive", e.target.checked)
                      }
                    />
                  }
                  label="Case Sensitive"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={params.searchDisplayNames || false}
                      onChange={(e) =>
                        updateParam("searchDisplayNames", e.target.checked)
                      }
                    />
                  }
                  label="Search Display Names"
                />
              </Box>
            </Grid>

            {/* Date Range */}
            <Grid item xs={12}>
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMore />}>
                  <Typography>Date Range</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <DateTimePicker
                        label="From"
                        value={params.from}
                        onChange={(value) => updateParam("from", value)}
                        slotProps={{ textField: { fullWidth: true } }}
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <DateTimePicker
                        label="To"
                        value={params.to}
                        onChange={(value) => updateParam("to", value)}
                        slotProps={{ textField: { fullWidth: true } }}
                      />
                    </Grid>
                  </Grid>
                </AccordionDetails>
              </Accordion>
            </Grid>

            {/* User Filters */}
            <Grid item xs={12}>
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMore />}>
                  <Typography>User Filters</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Autocomplete
                        freeSolo
                        multiple
                        options={[]}
                        value={params.users || []}
                        onChange={(_, value) => updateParam("users", value)}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Include Users"
                            placeholder="Add usernames..."
                          />
                        )}
                        renderTags={(value, getTagProps) =>
                          value.map((option, index) => (
                            <FilterChip
                              variant="outlined"
                              label={option}
                              {...getTagProps({ index })}
                              key={option}
                            />
                          ))
                        }
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <Autocomplete
                        freeSolo
                        multiple
                        options={[]}
                        value={params.excludeUsers || []}
                        onChange={(_, value) =>
                          updateParam("excludeUsers", value)
                        }
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Exclude Users"
                            placeholder="Add usernames to exclude..."
                          />
                        )}
                        renderTags={(value, getTagProps) =>
                          value.map((option, index) => (
                            <FilterChip
                              variant="outlined"
                              color="error"
                              label={option}
                              {...getTagProps({ index })}
                              key={option}
                            />
                          ))
                        }
                      />
                    </Grid>
                  </Grid>
                </AccordionDetails>
              </Accordion>
            </Grid>

            {/* Message Type Filters */}
            <Grid item xs={12}>
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMore />}>
                  <Typography>Message Types</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Autocomplete
                    multiple
                    options={messageTypeOptions}
                    getOptionLabel={(option) => option.label}
                    value={messageTypeOptions.filter((opt) =>
                      (params.messageTypes || []).includes(opt.value)
                    )}
                    onChange={(_, value) =>
                      updateParam(
                        "messageTypes",
                        value.map((v) => v.value)
                      )
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Message Types"
                        placeholder="Select message types..."
                      />
                    )}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <FilterChip
                          variant="outlined"
                          label={option.label}
                          {...getTagProps({ index })}
                          key={option.value}
                        />
                      ))
                    }
                  />
                </AccordionDetails>
              </Accordion>
            </Grid>

            {/* Badge/Role Filters */}
            <Grid item xs={12}>
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMore />}>
                  <Typography>User Roles & Badges</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Box display="flex" flexWrap="wrap" gap={2}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={params.subscribersOnly || false}
                              onChange={(e) =>
                                updateParam("subscribersOnly", e.target.checked)
                              }
                            />
                          }
                          label="Subscribers Only"
                        />
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={params.vipsOnly || false}
                              onChange={(e) =>
                                updateParam("vipsOnly", e.target.checked)
                              }
                            />
                          }
                          label="VIPs Only"
                        />
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={params.moderatorsOnly || false}
                              onChange={(e) =>
                                updateParam("moderatorsOnly", e.target.checked)
                              }
                            />
                          }
                          label="Moderators Only"
                        />
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={params.firstMessagesOnly || false}
                              onChange={(e) =>
                                updateParam(
                                  "firstMessagesOnly",
                                  e.target.checked
                                )
                              }
                            />
                          }
                          label="First Messages Only"
                        />
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={params.hasEmotes || false}
                              onChange={(e) =>
                                updateParam("hasEmotes", e.target.checked)
                              }
                            />
                          }
                          label="Has Emotes"
                        />
                      </Box>
                    </Grid>
                  </Grid>
                </AccordionDetails>
              </Accordion>
            </Grid>

            {/* Message Length Filters */}
            <Grid item xs={12}>
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMore />}>
                  <Typography>Message Length</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        type="number"
                        label="Minimum Length"
                        value={params.minLength || ""}
                        onChange={(e) =>
                          updateParam(
                            "minLength",
                            e.target.value
                              ? parseInt(e.target.value)
                              : undefined
                          )
                        }
                        inputProps={{ min: 0 }}
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        type="number"
                        label="Maximum Length"
                        value={params.maxLength || ""}
                        onChange={(e) =>
                          updateParam(
                            "maxLength",
                            e.target.value
                              ? parseInt(e.target.value)
                              : undefined
                          )
                        }
                        inputProps={{ min: 0 }}
                      />
                    </Grid>
                  </Grid>
                </AccordionDetails>
              </Accordion>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClear} startIcon={<Clear />}>
            Clear
          </Button>
          <Button onClick={onClose}>Cancel</Button>
          <Button
            onClick={handleSearch}
            variant="contained"
            startIcon={<Search />}
          >
            Search
          </Button>
        </DialogActions>
      </StyledDialog>
    </LocalizationProvider>
  );
}
