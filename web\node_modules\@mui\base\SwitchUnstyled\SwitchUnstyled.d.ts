import { OverridableComponent } from '@mui/types';
import { SwitchUnstyledTypeMap } from './SwitchUnstyled.types';
/**
 * The foundation for building custom-styled switches.
 *
 * Demos:
 *
 * - [Unstyled Switch](https://mui.com/base/react-switch/)
 *
 * API:
 *
 * - [SwitchUnstyled API](https://mui.com/base/api/switch-unstyled/)
 */
declare const SwitchUnstyled: OverridableComponent<SwitchUnstyledTypeMap<{}, "span">>;
export default SwitchUnstyled;
