export interface SelectChangeNotifiers<TValue> {
    /**
     * Calls all the registered selection change handlers.
     *
     * @param newValue - The newly selected value(s).
     */
    notifySelectionChanged: (newValue: TValue | TValue[] | null) => void;
    /**
     * Calls all the registered highlight change handlers.
     *
     * @param newValue - The newly highlighted value.
     */
    notifyHighlightChanged: (newValue: TValue | null) => void;
    registerSelectionChangeHandler: (handler: (newValue: TValue | TValue[] | null) => void) => () => void;
    registerHighlightChangeHandler: (handler: (newValue: TValue | null) => void) => () => void;
}
/**
 * @ignore - internal hook.
 *
 * This hook is used to notify any interested components about changes in the Select's selection and highlight.
 */
export default function useSelectChangeNotifiers<TValue>(): SelectChangeNotifiers<TValue>;
