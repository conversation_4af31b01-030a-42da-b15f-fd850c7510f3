import { OverridableComponent } from '@mui/types';
import { PopperUnstyledTypeMap } from './PopperUnstyled.types';
/**
 * Poppers rely on the 3rd party library [Popper.js](https://popper.js.org/docs/v2/) for positioning.
 *
 * Demos:
 *
 * - [Unstyled Popper](https://mui.com/base/react-popper/)
 *
 * API:
 *
 * - [PopperUnstyled API](https://mui.com/base/api/popper-unstyled/)
 */
declare const PopperUnstyled: OverridableComponent<PopperUnstyledTypeMap<{}, "div">>;
export default PopperUnstyled;
