{"name": "ramda-adjunct", "description": "Ramda Adjunct is the most popular and most comprehensive set of utilities for use with Ramda, providing a variety of useful, well tested functions with excellent documentation.", "keywords": ["ramda", "utils", "utilities", "toolkit", "extensions", "addons", "adjunct", "recipes", "extras", "cookbook", "functional"], "version": "3.4.0", "homepage": "https://github.com/char0n/ramda-adjunct", "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git://github.com/char0n/ramda-adjunct.git"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/ramda-adjunct"}, "engines": {"node": ">=0.10.3"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.linkedin.com/in/vladimirgorej/"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/srghma"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Undistraction"}, {"name": "<PERSON>", "url": "https://github.com/rockymadden"}, {"name": "Guillaume ARM", "email": "<EMAIL>", "url": "https://github.com/guillaumearm"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/js636f"}], "sideEffects": false, "main": "./lib/index.js", "module": "./es/index.js", "jsnext:main": "./es/index.js", "types": "types/index.d.ts", "scripts": {"docs": "better-npm-run docs", "lint": "better-npm-run lint", "lint:fix": "better-npm-run lint:fix", "changelog": "better-npm-run changelog", "test": "better-npm-run test", "test:web": "better-npm-run test:web", "test:ramda": "bnr test:ramda", "test:types": "eslint --no-eslintrc --no-ignore -c ./types/dtslint.eslint.js ./types/test/*.ts", "test:bundle-create": "better-npm-run test:bundle-create", "test:bundle-clean": "better-npm-run test:bundle-clean", "coverage": "better-npm-run coverage", "build": "better-npm-run build", "build:es": "better-npm-run build:es", "build:commonjs": "better-npm-run build:commonjs", "build:umd": "better-npm-run build:umd", "prepublishOnly": "better-npm-run prepublishOnly", "clean": "better-npm-run clean"}, "betterScripts": {"docs": "jsdoc -c jsdoc.json && node ./scripts/jsdoc-inject-dist.js", "lint": "eslint ./ && eslint --no-ignore --no-eslintrc -c ./types/.eslintrc.js ./types/test/**/*.ts", "lint:fix": "eslint --fix ./ && eslint --no-ignore --no-eslintrc --fix -c ./types/.eslintrc.js ./types/test/**/*.ts", "changelog": "conventional-changelog -p angular -i ./CHANGELOG.md -s", "test": {"command": "mocha", "env": {"BABEL_ENV": "commonjs"}}, "test:web": "testem ci", "test:ramda": "bnr test:ramda0.28.0", "test:ramda0.28.0": "npm install ramda@0.28.0 && npm test", "test:bundle-create": "webpack --config webpack.config-test.web.js --progress", "test:bundle-clean": "rimraf tmp-test-bundle.js", "coverage": {"command": "nyc mocha", "env": {"BABEL_ENV": "coverage"}}, "build": "npm run build:es && npm run build:commonjs && npm run build:umd", "build:es": {"command": "babel src --out-dir es", "env": {"BABEL_ENV": "es"}}, "build:commonjs": {"command": "babel src --out-dir lib", "env": {"BABEL_ENV": "commonjs"}}, "build:umd": "webpack --config webpack.config.js --progress", "prepublishOnly": "npm run clean && npm run lint && npm run test && npm run build && npm run docs", "clean": "rimraf .nyc_output .tmp docs coverage tmp-test-bundle.js dist lib es"}, "peerDependencies": {"ramda": ">= 0.28.0 <= 0.28.0"}, "devDependencies": {"@babel/cli": "7.19.3", "@babel/core": "=7.20.5", "@babel/plugin-transform-modules-commonjs": "=7.19.6", "@babel/preset-env": "=7.20.2", "@babel/register": "7.18.9", "@commitlint/cli": "=17.3.0", "@commitlint/config-conventional": "=17.3.0", "@typescript-eslint/eslint-plugin": "=5.45.0", "@typescript-eslint/parser": "=5.45.0", "assert": "=2.0.0", "babel-loader": "=9.1.0", "babel-plugin-annotate-pure-calls": "0.4.0", "babel-plugin-istanbul": "6.1.1", "better-npm-run": "0.1.1", "chai": "4.3.7", "codecov": "3.8.3", "conventional-changelog-cli": "2.2.2", "core-js": "=3.26.1", "docdash": "git+https://github.com/char0n/docdash.git#534b44382138a55dd8d93642c979e51e46471185", "eslint": "=8.29.0", "eslint-config-airbnb-base": "=15.0.0", "eslint-config-prettier": "=8.5.0", "eslint-plugin-dtslint": "=3.0.1", "eslint-plugin-import": "=2.26.0", "eslint-plugin-mocha": "=10.1.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-ramda": "2.5.1", "fantasy-land": "5.0.0", "fantasy-laws": "=2.0.1", "folktale": "=2.3.2", "glob": "=8.0.3", "husky": "8.0.2", "istanbul": "=0.4.5", "jsdoc": "=4.0.0", "jsverify": "0.8.4", "license-cli": "1.1.6", "lint-staged": "13.0.4", "mocha": "=10.1.0", "mocha-junit-reporter": "2.2.0", "mocha-multi-reporters": "1.5.1", "monet": "0.9.3", "nyc": "15.1.0", "prettier": "=2.8.0", "process": "=0.11.10", "ramda": "=0.28.0", "ramda-fantasy": "=0.8.0", "regenerator-runtime": "=0.13.11", "rimraf": "3.0.2", "sanctuary-show": "3.0.0", "sinon": "=15.0.0", "terser-webpack-plugin": "5.3.6", "testem": "=3.10.0", "typescript": "=4.9.3", "webpack": "=5.75.0", "webpack-cli": "5.0.0", "taffydb": "npm:@jsdoc/salty@0.2.1"}, "browserslist": "> 0.25%, ie 10, ie 11, not op_mini all", "tonicExampleFilename": "tonicExample.js", "collective": {"type": "opencollective", "url": "https://opencollective.com/ramda-adjunct", "logo": "https://opencollective.com/ramda-adjunct/logo.txt"}}