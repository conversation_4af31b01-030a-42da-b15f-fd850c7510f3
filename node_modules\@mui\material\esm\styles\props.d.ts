import { AlertProps } from "../Alert/index.js";
import { AlertTitleProps } from "../AlertTitle/index.js";
import { AppBarProps } from "../AppBar/index.js";
import { AutocompleteProps } from "../Autocomplete/index.js";
import { AvatarProps } from "../Avatar/index.js";
import { AvatarGroupProps } from "../AvatarGroup/index.js";
import { BackdropProps } from "../Backdrop/index.js";
import { BadgeProps } from "../Badge/index.js";
import { BottomNavigationActionProps } from "../BottomNavigationAction/index.js";
import { BottomNavigationProps } from "../BottomNavigation/index.js";
import { BreadcrumbsProps } from "../Breadcrumbs/index.js";
import { ButtonBaseProps } from "../ButtonBase/index.js";
import { ButtonGroupProps } from "../ButtonGroup/index.js";
import { ButtonProps } from "../Button/index.js";
import { CardActionAreaProps } from "../CardActionArea/index.js";
import { CardActionsProps } from "../CardActions/index.js";
import { CardContentProps } from "../CardContent/index.js";
import { CardHeaderProps } from "../CardHeader/index.js";
import { CardMediaProps } from "../CardMedia/index.js";
import { CardProps } from "../Card/index.js";
import { CheckboxProps } from "../Checkbox/index.js";
import { ChipProps } from "../Chip/index.js";
import { CircularProgressProps } from "../CircularProgress/index.js";
import { CollapseProps } from "../Collapse/index.js";
import { ContainerProps } from "../Container/index.js";
import { CssBaselineProps } from "../CssBaseline/index.js";
import { DialogActionsProps } from "../DialogActions/index.js";
import { DialogContentProps } from "../DialogContent/index.js";
import { DialogContentTextProps } from "../DialogContentText/index.js";
import { DialogProps } from "../Dialog/index.js";
import { DialogTitleProps } from "../DialogTitle/index.js";
import { DividerProps } from "../Divider/index.js";
import { DrawerProps } from "../Drawer/index.js";
import { AccordionActionsProps } from "../AccordionActions/index.js";
import { AccordionDetailsProps } from "../AccordionDetails/index.js";
import { AccordionProps } from "../Accordion/index.js";
import { AccordionSummaryProps } from "../AccordionSummary/index.js";
import { FabProps } from "../Fab/index.js";
import { FilledInputProps } from "../FilledInput/index.js";
import { FormControlLabelProps } from "../FormControlLabel/index.js";
import { FormControlProps } from "../FormControl/index.js";
import { FormGroupProps } from "../FormGroup/index.js";
import { FormHelperTextProps } from "../FormHelperText/index.js";
import { FormLabelProps } from "../FormLabel/index.js";
import { GridLegacyProps } from "../GridLegacy/index.js";
import { GridProps } from "../Grid/index.js";
import { IconButtonProps } from "../IconButton/index.js";
import { IconProps } from "../Icon/index.js";
import { ImageListProps } from "../ImageList/index.js";
import { ImageListItemBarProps } from "../ImageListItemBar/index.js";
import { ImageListItemProps } from "../ImageListItem/index.js";
import { InputAdornmentProps } from "../InputAdornment/index.js";
import { InputBaseProps } from "../InputBase/index.js";
import { InputLabelProps } from "../InputLabel/index.js";
import { InputProps } from "../Input/index.js";
import { LinearProgressProps } from "../LinearProgress/index.js";
import { LinkProps } from "../Link/index.js";
import { ListItemAvatarProps } from "../ListItemAvatar/index.js";
import { ListItemIconProps } from "../ListItemIcon/index.js";
import { ListItemProps } from "../ListItem/index.js";
import { ListItemButtonProps } from "../ListItemButton/index.js";
import { ListItemSecondaryActionProps } from "../ListItemSecondaryAction/index.js";
import { ListItemTextProps } from "../ListItemText/index.js";
import { ListProps } from "../List/index.js";
import { ListSubheaderProps } from "../ListSubheader/index.js";
import { MenuItemProps } from "../MenuItem/index.js";
import { MenuListProps } from "../MenuList/index.js";
import { MenuProps } from "../Menu/index.js";
import { MobileStepperProps } from "../MobileStepper/index.js";
import { ModalProps } from "../Modal/index.js";
import { NativeSelectProps } from "../NativeSelect/index.js";
import { UseMediaQueryOptions } from "../useMediaQuery/index.js";
import { OutlinedInputProps } from "../OutlinedInput/index.js";
import { PaginationProps } from "../Pagination/index.js";
import { PaginationItemProps } from "../PaginationItem/index.js";
import { PaperProps } from "../Paper/index.js";
import { PopoverProps } from "../Popover/index.js";
import { RadioGroupProps } from "../RadioGroup/index.js";
import { RadioProps } from "../Radio/index.js";
import { RatingProps } from "../Rating/index.js";
import { ScopedCssBaselineProps } from "../ScopedCssBaseline/index.js";
import { SelectProps } from "../Select/index.js";
import { SkeletonProps } from "../Skeleton/index.js";
import { SliderProps } from "../Slider/index.js";
import { SnackbarContentProps } from "../SnackbarContent/index.js";
import { SnackbarProps } from "../Snackbar/index.js";
import { SpeedDialProps } from "../SpeedDial/index.js";
import { SpeedDialActionProps } from "../SpeedDialAction/index.js";
import { SpeedDialIconProps } from "../SpeedDialIcon/index.js";
import { StackProps } from "../Stack/index.js";
import { StepButtonProps } from "../StepButton/index.js";
import { StepConnectorProps } from "../StepConnector/index.js";
import { StepContentProps } from "../StepContent/index.js";
import { StepIconProps } from "../StepIcon/index.js";
import { StepLabelProps } from "../StepLabel/index.js";
import { StepperProps } from "../Stepper/index.js";
import { StepProps } from "../Step/index.js";
import { SvgIconProps } from "../SvgIcon/index.js";
import { SwipeableDrawerProps } from "../SwipeableDrawer/index.js";
import { SwitchProps } from "../Switch/index.js";
import { TableBodyProps } from "../TableBody/index.js";
import { TableCellProps } from "../TableCell/index.js";
import { TableContainerProps } from "../TableContainer/index.js";
import { TableHeadProps } from "../TableHead/index.js";
import { TablePaginationProps } from "../TablePagination/index.js";
import { TablePaginationActionsProps } from "../TablePaginationActions/index.js";
import { TableProps } from "../Table/index.js";
import { TableRowProps } from "../TableRow/index.js";
import { TableSortLabelProps } from "../TableSortLabel/index.js";
import { TableFooterProps } from "../TableFooter/index.js";
import { TabProps } from "../Tab/index.js";
import { TabsProps } from "../Tabs/index.js";
import { TextFieldProps } from "../TextField/index.js";
import { ToggleButtonProps } from "../ToggleButton/index.js";
import { ToggleButtonGroupProps } from "../ToggleButtonGroup/index.js";
import { ToolbarProps } from "../Toolbar/index.js";
import { TooltipProps } from "../Tooltip/index.js";
import { TouchRippleProps } from "../ButtonBase/TouchRipple.js";
import { TypographyProps } from "../Typography/index.js";
import { PopperProps } from "../Popper/index.js";
export type ComponentsProps = { [Name in keyof ComponentsPropsList]?: Partial<ComponentsPropsList[Name]> };
export interface ComponentsPropsList {
  MuiAlert: AlertProps;
  MuiAlertTitle: AlertTitleProps;
  MuiAppBar: AppBarProps;
  MuiAutocomplete: AutocompleteProps<any, any, any, any>;
  MuiAvatar: AvatarProps;
  MuiAvatarGroup: AvatarGroupProps;
  MuiBackdrop: BackdropProps;
  MuiBadge: BadgeProps;
  MuiBottomNavigation: BottomNavigationProps;
  MuiBottomNavigationAction: BottomNavigationActionProps;
  MuiBreadcrumbs: BreadcrumbsProps;
  MuiButton: ButtonProps;
  MuiButtonBase: ButtonBaseProps;
  MuiButtonGroup: ButtonGroupProps;
  MuiCard: CardProps;
  MuiCardActionArea: CardActionAreaProps;
  MuiCardActions: CardActionsProps;
  MuiCardContent: CardContentProps;
  MuiCardHeader: CardHeaderProps;
  MuiCardMedia: CardMediaProps;
  MuiCheckbox: CheckboxProps;
  MuiChip: ChipProps;
  MuiCircularProgress: CircularProgressProps;
  MuiCollapse: CollapseProps;
  MuiContainer: ContainerProps;
  MuiCssBaseline: CssBaselineProps;
  MuiDialog: DialogProps;
  MuiDialogActions: DialogActionsProps;
  MuiDialogContent: DialogContentProps;
  MuiDialogContentText: DialogContentTextProps;
  MuiDialogTitle: DialogTitleProps;
  MuiDivider: DividerProps;
  MuiDrawer: DrawerProps;
  MuiAccordion: AccordionProps;
  MuiAccordionActions: AccordionActionsProps;
  MuiAccordionDetails: AccordionDetailsProps;
  MuiAccordionSummary: AccordionSummaryProps;
  MuiFab: FabProps;
  MuiFilledInput: FilledInputProps;
  MuiFormControl: FormControlProps;
  MuiFormControlLabel: FormControlLabelProps;
  MuiFormGroup: FormGroupProps;
  MuiFormHelperText: FormHelperTextProps;
  MuiFormLabel: FormLabelProps;
  MuiGridLegacy: GridLegacyProps;
  MuiGrid: GridProps;
  MuiImageList: ImageListProps;
  MuiImageListItem: ImageListItemProps;
  MuiImageListItemBar: ImageListItemBarProps;
  MuiIcon: IconProps;
  MuiIconButton: IconButtonProps;
  MuiInput: InputProps;
  MuiInputAdornment: InputAdornmentProps;
  MuiInputBase: InputBaseProps;
  MuiInputLabel: InputLabelProps;
  MuiLinearProgress: LinearProgressProps;
  MuiLink: LinkProps;
  MuiList: ListProps;
  MuiListItem: ListItemProps;
  MuiListItemButton: ListItemButtonProps;
  MuiListItemAvatar: ListItemAvatarProps;
  MuiListItemIcon: ListItemIconProps;
  MuiListItemSecondaryAction: ListItemSecondaryActionProps;
  MuiListItemText: ListItemTextProps;
  MuiListSubheader: ListSubheaderProps;
  MuiMenu: MenuProps;
  MuiMenuItem: MenuItemProps;
  MuiMenuList: MenuListProps;
  MuiMobileStepper: MobileStepperProps;
  MuiModal: ModalProps;
  MuiNativeSelect: NativeSelectProps;
  MuiOutlinedInput: OutlinedInputProps;
  MuiPagination: PaginationProps;
  MuiPaginationItem: PaginationItemProps;
  MuiPaper: PaperProps;
  MuiPopper: PopperProps;
  MuiPopover: PopoverProps;
  MuiRadio: RadioProps;
  MuiRadioGroup: RadioGroupProps;
  MuiRating: RatingProps;
  MuiScopedCssBaseline: ScopedCssBaselineProps;
  MuiSelect: SelectProps;
  MuiSkeleton: SkeletonProps;
  MuiSlider: SliderProps;
  MuiSnackbar: SnackbarProps;
  MuiSnackbarContent: SnackbarContentProps;
  MuiSpeedDial: SpeedDialProps;
  MuiSpeedDialAction: SpeedDialActionProps;
  MuiSpeedDialIcon: SpeedDialIconProps;
  MuiStack: StackProps;
  MuiStep: StepProps;
  MuiStepButton: StepButtonProps;
  MuiStepConnector: StepConnectorProps;
  MuiStepContent: StepContentProps;
  MuiStepIcon: StepIconProps;
  MuiStepLabel: StepLabelProps;
  MuiStepper: StepperProps;
  MuiSvgIcon: SvgIconProps;
  MuiSwipeableDrawer: SwipeableDrawerProps;
  MuiSwitch: SwitchProps;
  MuiTab: TabProps;
  MuiTable: TableProps;
  MuiTableBody: TableBodyProps;
  MuiTableCell: TableCellProps;
  MuiTableContainer: TableContainerProps;
  MuiTableFooter: TableFooterProps;
  MuiTableHead: TableHeadProps;
  MuiTablePagination: TablePaginationProps;
  MuiTablePaginationActions: TablePaginationActionsProps;
  MuiTableRow: TableRowProps;
  MuiTableSortLabel: TableSortLabelProps;
  MuiTabs: TabsProps;
  MuiTextField: TextFieldProps;
  MuiToggleButton: ToggleButtonProps;
  MuiToggleButtonGroup: ToggleButtonGroupProps;
  MuiToolbar: ToolbarProps;
  MuiTooltip: TooltipProps;
  MuiTouchRipple: TouchRippleProps;
  MuiTypography: TypographyProps;
  MuiUseMediaQuery: UseMediaQueryOptions;
}