import { CSSObject, CSSInterpolation, Interpolation } from '@mui/system';
import { PopperClassKey } from "../Popper/index.js";
import { ComponentsPropsList } from "./props.js";
import { AccordionActionsClassKey } from "../AccordionActions/index.js";
import { AccordionClassKey } from "../Accordion/index.js";
import { AccordionDetailsClassKey } from "../AccordionDetails/index.js";
import { AccordionSummaryClassKey } from "../AccordionSummary/index.js";
import { AlertClassKey } from "../Alert/index.js";
import { AlertTitleClassKey } from "../AlertTitle/index.js";
import { AppBarClassKey } from "../AppBar/index.js";
import { AutocompleteClassKey } from "../Autocomplete/index.js";
import { AvatarClassKey } from "../Avatar/index.js";
import { AvatarGroupClassKey } from "../AvatarGroup/index.js";
import { BackdropClassKey } from "../Backdrop/index.js";
import { BadgeClassKey } from "../Badge/index.js";
import { BottomNavigationActionClassKey } from "../BottomNavigationAction/index.js";
import { BottomNavigationClassKey } from "../BottomNavigation/index.js";
import { BreadcrumbsClassKey } from "../Breadcrumbs/index.js";
import { ButtonBaseClassKey } from "../ButtonBase/index.js";
import { ButtonClassKey } from "../Button/index.js";
import { ButtonGroupClassKey } from "../ButtonGroup/index.js";
import { CardActionAreaClassKey } from "../CardActionArea/index.js";
import { CardActionsClassKey } from "../CardActions/index.js";
import { CardClassKey } from "../Card/index.js";
import { CardContentClassKey } from "../CardContent/index.js";
import { CardHeaderClassKey } from "../CardHeader/index.js";
import { CardMediaClassKey } from "../CardMedia/index.js";
import { CheckboxClassKey } from "../Checkbox/index.js";
import { ChipClassKey } from "../Chip/index.js";
import { CircularProgressClassKey } from "../CircularProgress/index.js";
import { CollapseClassKey } from "../Collapse/index.js";
import { ContainerClassKey } from "../Container/index.js";
import { DialogActionsClassKey } from "../DialogActions/index.js";
import { DialogClassKey } from "../Dialog/index.js";
import { DialogContentClassKey } from "../DialogContent/index.js";
import { DialogContentTextClassKey } from "../DialogContentText/index.js";
import { DialogTitleClassKey } from "../DialogTitle/index.js";
import { DividerClassKey } from "../Divider/index.js";
import { DrawerClassKey } from "../Drawer/index.js";
import { FabClassKey } from "../Fab/index.js";
import { FilledInputClassKey } from "../FilledInput/index.js";
import { FormControlClassKey } from "../FormControl/index.js";
import { FormControlLabelClassKey } from "../FormControlLabel/index.js";
import { FormGroupClassKey } from "../FormGroup/index.js";
import { FormHelperTextClassKey } from "../FormHelperText/index.js";
import { FormLabelClassKey } from "../FormLabel/index.js";
import { GridLegacyClassKey } from "../GridLegacy/index.js";
import { GridClassKey } from "../Grid/index.js";
import { IconButtonClassKey } from "../IconButton/index.js";
import { IconClassKey } from "../Icon/index.js";
import { ImageListClassKey } from "../ImageList/index.js";
import { ImageListItemBarClassKey } from "../ImageListItemBar/index.js";
import { ImageListItemClassKey } from "../ImageListItem/index.js";
import { InputAdornmentClassKey } from "../InputAdornment/index.js";
import { InputBaseClassKey } from "../InputBase/index.js";
import { InputClassKey } from "../Input/index.js";
import { InputLabelClassKey } from "../InputLabel/index.js";
import { LinearProgressClassKey } from "../LinearProgress/index.js";
import { LinkClassKey } from "../Link/index.js";
import { ListClassKey } from "../List/index.js";
import { ListItemAvatarClassKey } from "../ListItemAvatar/index.js";
import { ListItemClassKey } from "../ListItem/index.js";
import { ListItemButtonClassKey } from "../ListItemButton/index.js";
import { ListItemIconClassKey } from "../ListItemIcon/index.js";
import { ListItemSecondaryActionClassKey } from "../ListItemSecondaryAction/index.js";
import { ListItemTextClassKey } from "../ListItemText/index.js";
import { ListSubheaderClassKey } from "../ListSubheader/index.js";
import { MenuClassKey } from "../Menu/index.js";
import { MenuItemClassKey } from "../MenuItem/index.js";
import { MenuListClassKey } from "../MenuList/index.js";
import { MobileStepperClassKey } from "../MobileStepper/index.js";
import { ModalClassKey } from "../Modal/index.js";
import { NativeSelectClassKey } from "../NativeSelect/index.js";
import { OutlinedInputClassKey } from "../OutlinedInput/index.js";
import { PaginationClassKey } from "../Pagination/index.js";
import { PaginationItemClassKey } from "../PaginationItem/index.js";
import { PaperClassKey } from "../Paper/index.js";
import { PopoverClassKey } from "../Popover/index.js";
import { RadioClassKey } from "../Radio/index.js";
import { RadioGroupClassKey } from "../RadioGroup/index.js";
import { RatingClassKey } from "../Rating/index.js";
import { ScopedCssBaselineClassKey } from "../ScopedCssBaseline/index.js";
import { SelectClassKey } from "../Select/index.js";
import { SkeletonClassKey } from "../Skeleton/index.js";
import { SliderClassKey } from "../Slider/index.js";
import { SnackbarClassKey } from "../Snackbar/index.js";
import { SnackbarContentClassKey } from "../SnackbarContent/index.js";
import { SpeedDialClassKey } from "../SpeedDial/index.js";
import { SpeedDialActionClassKey } from "../SpeedDialAction/index.js";
import { SpeedDialIconClassKey } from "../SpeedDialIcon/index.js";
import { StackClassKey } from "../Stack/index.js";
import { StepButtonClasskey } from "../StepButton/index.js";
import { StepClasskey } from "../Step/index.js";
import { StepConnectorClasskey } from "../StepConnector/index.js";
import { StepContentClasskey } from "../StepContent/index.js";
import { StepIconClasskey } from "../StepIcon/index.js";
import { StepLabelClasskey } from "../StepLabel/index.js";
import { StepperClasskey } from "../Stepper/index.js";
import { SvgIconClassKey } from "../SvgIcon/index.js";
import { SwitchClassKey } from "../Switch/index.js";
import { TabClassKey } from "../Tab/index.js";
import { TableBodyClassKey } from "../TableBody/index.js";
import { TableCellClassKey } from "../TableCell/index.js";
import { TableClassKey } from "../Table/index.js";
import { TableContainerClassKey } from "../TableContainer/index.js";
import { TableFooterClassKey } from "../TableFooter/index.js";
import { TableHeadClassKey } from "../TableHead/index.js";
import { TablePaginationClassKey } from "../TablePagination/index.js";
import { TablePaginationActionsClassKey } from "../TablePaginationActions/index.js";
import { TableRowClassKey } from "../TableRow/index.js";
import { TableSortLabelClassKey } from "../TableSortLabel/index.js";
import { TabsClassKey } from "../Tabs/index.js";
import { TextFieldClassKey } from "../TextField/index.js";
import { ToggleButtonClassKey } from "../ToggleButton/index.js";
import { ToggleButtonGroupClassKey } from "../ToggleButtonGroup/index.js";
import { ToolbarClassKey } from "../Toolbar/index.js";
import { TooltipClassKey } from "../Tooltip/index.js";
import { TouchRippleClassKey } from "../ButtonBase/TouchRipple.js";
import { TypographyClassKey } from "../Typography/index.js";
export type OverridesStyleRules<ClassKey extends string = string, ComponentName = keyof ComponentsPropsList, Theme = unknown> = Record<ClassKey, Interpolation<(ComponentName extends keyof ComponentsPropsList ? ComponentsPropsList[ComponentName] & Record<string, unknown> & {
  ownerState: ComponentsPropsList[ComponentName] & Record<string, unknown>;
} : {}) & {
  theme: Theme;
} & Record<string, unknown>>>;
export type ComponentsOverrides<Theme = unknown> = { [Name in keyof ComponentNameToClassKey]?: Partial<OverridesStyleRules<ComponentNameToClassKey[Name], Name, Theme>> } & {
  MuiCssBaseline?: CSSObject | string | ((theme: Theme) => CSSInterpolation);
};
export interface ComponentNameToClassKey {
  MuiAlert: AlertClassKey;
  MuiAlertTitle: AlertTitleClassKey;
  MuiAppBar: AppBarClassKey;
  MuiAutocomplete: AutocompleteClassKey;
  MuiAvatar: AvatarClassKey;
  MuiAvatarGroup: AvatarGroupClassKey;
  MuiBackdrop: BackdropClassKey;
  MuiBadge: BadgeClassKey;
  MuiBottomNavigation: BottomNavigationClassKey;
  MuiBottomNavigationAction: BottomNavigationActionClassKey;
  MuiBreadcrumbs: BreadcrumbsClassKey;
  MuiButton: ButtonClassKey;
  MuiButtonBase: ButtonBaseClassKey;
  MuiButtonGroup: ButtonGroupClassKey;
  MuiCard: CardClassKey;
  MuiCardActionArea: CardActionAreaClassKey;
  MuiCardActions: CardActionsClassKey;
  MuiCardContent: CardContentClassKey;
  MuiCardHeader: CardHeaderClassKey;
  MuiCardMedia: CardMediaClassKey;
  MuiCheckbox: CheckboxClassKey;
  MuiChip: ChipClassKey;
  MuiCircularProgress: CircularProgressClassKey;
  MuiCollapse: CollapseClassKey;
  MuiContainer: ContainerClassKey;
  MuiDialog: DialogClassKey;
  MuiDialogActions: DialogActionsClassKey;
  MuiDialogContent: DialogContentClassKey;
  MuiDialogContentText: DialogContentTextClassKey;
  MuiDialogTitle: DialogTitleClassKey;
  MuiDivider: DividerClassKey;
  MuiDrawer: DrawerClassKey;
  MuiAccordion: AccordionClassKey;
  MuiAccordionActions: AccordionActionsClassKey;
  MuiAccordionDetails: AccordionDetailsClassKey;
  MuiAccordionSummary: AccordionSummaryClassKey;
  MuiFab: FabClassKey;
  MuiFilledInput: FilledInputClassKey;
  MuiFormControl: FormControlClassKey;
  MuiFormControlLabel: FormControlLabelClassKey;
  MuiFormGroup: FormGroupClassKey;
  MuiFormHelperText: FormHelperTextClassKey;
  MuiFormLabel: FormLabelClassKey;
  MuiGridLegacy: GridLegacyClassKey;
  MuiGrid: GridClassKey;
  MuiIcon: IconClassKey;
  MuiIconButton: IconButtonClassKey;
  MuiImageList: ImageListClassKey;
  MuiImageListItem: ImageListItemClassKey;
  MuiImageListItemBar: ImageListItemBarClassKey;
  MuiInput: InputClassKey;
  MuiInputAdornment: InputAdornmentClassKey;
  MuiInputBase: InputBaseClassKey;
  MuiInputLabel: InputLabelClassKey;
  MuiLinearProgress: LinearProgressClassKey;
  MuiLink: LinkClassKey;
  MuiList: ListClassKey;
  MuiListItem: ListItemClassKey;
  MuiListItemButton: ListItemButtonClassKey;
  MuiListItemAvatar: ListItemAvatarClassKey;
  MuiListItemIcon: ListItemIconClassKey;
  MuiListItemSecondaryAction: ListItemSecondaryActionClassKey;
  MuiListItemText: ListItemTextClassKey;
  MuiListSubheader: ListSubheaderClassKey;
  MuiMenu: MenuClassKey;
  MuiMenuItem: MenuItemClassKey;
  MuiMenuList: MenuListClassKey;
  MuiMobileStepper: MobileStepperClassKey;
  MuiModal: ModalClassKey;
  MuiNativeSelect: NativeSelectClassKey;
  MuiOutlinedInput: OutlinedInputClassKey;
  MuiPagination: PaginationClassKey;
  MuiPaginationItem: PaginationItemClassKey;
  MuiPaper: PaperClassKey;
  MuiPopover: PopoverClassKey;
  MuiPopper: PopperClassKey;
  MuiRadio: RadioClassKey;
  MuiRadioGroup: RadioGroupClassKey;
  MuiRating: RatingClassKey;
  MuiScopedCssBaseline: ScopedCssBaselineClassKey;
  MuiSelect: SelectClassKey;
  MuiSkeleton: SkeletonClassKey;
  MuiSlider: SliderClassKey;
  MuiSnackbar: SnackbarClassKey;
  MuiSnackbarContent: SnackbarContentClassKey;
  MuiSpeedDial: SpeedDialClassKey;
  MuiSpeedDialAction: SpeedDialActionClassKey;
  MuiSpeedDialIcon: SpeedDialIconClassKey;
  MuiStack: StackClassKey;
  MuiStep: StepClasskey;
  MuiStepButton: StepButtonClasskey;
  MuiStepConnector: StepConnectorClasskey;
  MuiStepContent: StepContentClasskey;
  MuiStepIcon: StepIconClasskey;
  MuiStepLabel: StepLabelClasskey;
  MuiStepper: StepperClasskey;
  MuiSvgIcon: SvgIconClassKey;
  MuiSwitch: SwitchClassKey;
  MuiTab: TabClassKey;
  MuiTable: TableClassKey;
  MuiTableBody: TableBodyClassKey;
  MuiTableCell: TableCellClassKey;
  MuiTableContainer: TableContainerClassKey;
  MuiTableFooter: TableFooterClassKey;
  MuiTableHead: TableHeadClassKey;
  MuiTablePagination: TablePaginationClassKey;
  MuiTablePaginationActions: TablePaginationActionsClassKey;
  MuiTableRow: TableRowClassKey;
  MuiTableSortLabel: TableSortLabelClassKey;
  MuiTabs: TabsClassKey;
  MuiTextField: TextFieldClassKey;
  MuiToggleButton: ToggleButtonClassKey;
  MuiToggleButtonGroup: ToggleButtonGroupClassKey;
  MuiToolbar: ToolbarClassKey;
  MuiTooltip: TooltipClassKey;
  MuiTouchRipple: TouchRippleClassKey;
  MuiTypography: TypographyClassKey;
}